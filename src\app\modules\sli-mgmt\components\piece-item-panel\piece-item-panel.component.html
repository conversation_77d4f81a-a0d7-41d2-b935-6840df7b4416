<div class="orll-piece-item-panel">
	<mat-expansion-panel class="orll-piece-item-panel__panel" expanded>
		<mat-expansion-panel-header>
			@if (containedPiece) {
				<mat-panel-title>
					<div class="mat-display-2 orll-piece-item-panel__desc font" [matTooltip]="containedPiece.product.description">
						{{ containedPiece.pieceQuantity }} {{ containedPiece.product.description }}
					</div>
					<div class="mat-display-2 orll-piece-item-panel__package" [matTooltip]="containedPiece.packagingType.description">
						{{ 'sli.piece.packages' | translate }}:
						{{ containedPiece.packagingType.description }}&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
						{{ 'sli.piece.item.weight' | translate }}: {{ containedPiece.grossWeight }}KG&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
						{{ 'sli.piece.dimensions' | translate }}: {{ containedPiece.dimensions.length }}CMx{{
							containedPiece.dimensions.width
						}}CMx{{ containedPiece.dimensions.height }}CM
					</div>
				</mat-panel-title>
			}
			<mat-panel-description>
				<div class="orll-piece-item-panel__total-items">
					<span
						>{{ 'sli.piece.item.total' | translate }}:
						{{ containedPiece ? containedPiece.containedItems.length : pieceItemList.length }}</span
					>
				</div>
				@if (containedPiece) {
					<button
						mat-icon-button
						color="primary"
						(click)="$event.stopPropagation(); deletePiece.emit()"
						class="orll-piece-item-panel__delete-button">
						<mat-icon>delete</mat-icon>
					</button>
				}
			</mat-panel-description>
		</mat-expansion-panel-header>

		<form [formGroup]="sliPieceItemForm">
			<div class="row">
				<mat-form-field appearance="outline" class="col-4" floatLabel="always">
					<mat-label>{{ 'sli.piece.item.description' | translate }}</mat-label>
					<input matInput formControlName="description" />
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label>{{ 'sli.piece.item.weight' | translate }}</mat-label>
					<input matInput formControlName="weight" />
					<span matSuffix class="unit">KG</span>
					@if (sliPieceItemForm.get('weight')?.hasError('pattern')) {
						<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
					}
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label>{{ 'sli.piece.item.quantity' | translate }}</mat-label>
					<input matInput formControlName="quantity" />
					@if (sliPieceItemForm.get('quantity')?.hasError('pattern')) {
						<mat-error>{{ 'sli.mgmt.pieceList.pattern.positiveNumber' | translate }}</mat-error>
					}
				</mat-form-field>

				<button mat-raised-button type="button" (click)="addPieceItem(pieceIndex)" class="col-1 orll-piece-item-panel__add-button">
					<mat-icon>add</mat-icon>
					{{ 'sli.piece.item.add' | translate }}
				</button>
			</div>
		</form>

		<mat-list>
			@for (pieceItem of containedPiece ? containedPiece.containedItems : pieceItemList; track $index; let pieceItemIndex = $index) {
				<mat-list-item>
					<div class="piece-item-content">
						<div class="item-info">
							<div class="item-description col-6">{{ pieceItem.product.description }}</div>
							<div class="item-weight col-3">
								{{ 'sli.piece.item.weight' | translate }}: <span class="value">{{ pieceItem.weight }}KG</span>
							</div>
							<div class="item-quantity col-3">
								{{ 'sli.piece.item.quantity' | translate }}: <span class="value">{{ pieceItem.itemQuantity }}</span>
							</div>
						</div>
						<button
							mat-icon-button
							color="primary"
							type="button"
							(click)="delPieceItem(pieceIndex, pieceItemIndex)"
							class="orll-piece-item-panel__delete-button">
							<mat-icon>delete</mat-icon>
						</button>
					</div>
				</mat-list-item>
				@if (!$last) {
					<mat-divider></mat-divider>
				}
			}
		</mat-list>
	</mat-expansion-panel>
</div>
