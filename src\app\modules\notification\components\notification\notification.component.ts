import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginator, MatPaginatorIntl } from '@angular/material/paginator';
import { TranslateModule } from '@ngx-translate/core';
import { NotificationEventType, NotificationListObj } from 'src/app/modules/notification/models/notification.model';
import { NotificationService } from '../../services/notification.service';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Router } from '@angular/router';
import { LogisticObjType } from '@shared/models/share-type.model';
import { SLI_TAB_INDEX } from '@shared/models/constant';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';

@Component({
	selector: 'orll-notification',
	imports: [MatIconModule, TranslateModule, MatPaginator, IataDateFormatPipe],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
	templateUrl: './notification.component.html',
	styleUrl: './notification.component.scss',
})
export class NotificationComponent implements OnChanges {
	@Input() showUnread = false;
	@Input() refreshNotification = false;
	@Input() hasPagination = false;

	@Output() showOpen = new EventEmitter<boolean>();

	dataLoading = false;
	totalRecords = 0;
	pageSize = 10;
	pageIndex = 0;
	readonly tablePageSizes: number[] = [10, 50, 100];
	notifications: NotificationListObj[] = [];
	eventTypeEnum = NotificationEventType;

	constructor(
		private readonly service: NotificationService,
		private readonly router: Router,
		private readonly cdr: ChangeDetectorRef
	) {}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['showUnread'] || changes['refreshNotification']) {
			this.loadData();
		}
	}

	private loadData() {
		const request: PaginationRequest = {
			pageNum: this.pageIndex + 1,
			pageSize: this.pageSize,
		};
		this.dataLoading = true;
		this.service.getNotificationPerPage(request, this.showUnread).subscribe({
			next: (res) => {
				this.notifications = res.rows;

				this.dataLoading = false;
				this.totalRecords = res.total;
				this.cdr.detectChanges();
			},
			error: () => {
				this.dataLoading = false;
			},
		});
	}

	onPageChange(event: any): void {
		this.pageIndex = event.pageIndex;
		this.pageSize = event.pageSize;
		this.loadData();
	}

	goToDetail(row: NotificationListObj) {
		let routePath = [];

		if (row.dataType === LogisticObjType.SLI) {
			routePath = ['/sli/edit', row.logisticsObject, SLI_TAB_INDEX];
		} else if (row.dataType === LogisticObjType.BOOKING_OPTION_REQUEST) {
			routePath = ['/quote'];
		} else if (row.dataType === LogisticObjType.BOOKING_REQUEST || row.dataType === LogisticObjType.BOOKING) {
			routePath = ['/booking'];
		} else {
			routePath = [`/${row.dataType.toLocaleLowerCase()}/edit`, row.logisticsObject];
		}

		this.showOpen.emit(false);
		this.router.navigate(routePath);
	}
}
