<div class="orll-sli-create-page iata-box">
	@if (source === sourceType.SLI_LIST_EDIT) {
		<mat-tab-group
			#tabGroup
			mat-stretch-tabs="false"
			mat-align-tabs="start"
			class="orll-sli-create-page__tab_group"
			dynamicHeight
			[selectedIndex]="selectedTabIndex"
			(selectedTabChange)="onTabChanged($event)">
			<mat-tab [label]="'common.mainHeader.mainNav.sli.piece' | translate">
				<div class="row margin-r-5">
					<div class="orll-sli-create-page_pieces col-12">
						<orll-sli-piece-list
							#sliPieceList
							[selectedTabIndex]="selectedTabIndex"
							[sliNumber]="sliNumber"
							[disableUpdate]="disableUpdate"
							(refreshDelegationRequest)="delegationRequestList.refreshData()">
						</orll-sli-piece-list>
					</div>
				</div>
			</mat-tab>
			<mat-tab [label]="'ecsd.title' | translate">
				@if (sliNumber) {
					<orll-ecsd-list #ecsdList [loId]="sliNumber" [loType]="sliModule"></orll-ecsd-list>
				}
			</mat-tab>

			<mat-tab [label]="'sli.mgmt.shipment' | translate" class="orll-sli-create-page__tab">
				<ng-template [ngTemplateOutlet]="sharedContent"></ng-template
			></mat-tab>

			<mat-tab [label]="'common.change.request.title' | translate">
				<div class="orll-sli-create-page__tab">
					<orll-version-history-list [loId]="sliNumber" [type]="'sli'" #versionList></orll-version-history-list>
				</div>
			</mat-tab>
			<mat-tab [label]="'common.delegation.request.tab' | translate">
				<div class="orll-sli-create-page__tab col-12">
					<orll-delegation-request-list #delegationRequestList [loId]="sliNumber"></orll-delegation-request-list>
				</div>
			</mat-tab>
			<mat-tab [label]="'subscription.title' | translate">
				<div class="orll-sli-create-page__tab col-12">
					<orll-subscription-request-list [param]="subscripitonParam"></orll-subscription-request-list>
				</div>
			</mat-tab>
		</mat-tab-group>
	} @else if (source === sourceType.ECSD_LIST && shipperInfo) {
		<ng-template [ngTemplateOutlet]="sharedContent"></ng-template>
	} @else if (source === sourceType.SLI_LIST_CREATE) {
		<orll-sli-piece-list
			#sliPieceList
			[selectedTabIndex]="selectedTabIndex"
			[sliNumber]="sliNumber"
			[disableUpdate]="disableUpdate"
			(refreshDelegationRequest)="delegationRequestList.refreshData()"
			[source]="source">
		</orll-sli-piece-list>
	} @else if (source === sourceType.PIECE_LIST) {
		@if (sliNumber) {
			<orll-ecsd-list #ecsdList [loId]="sliNumber" [loType]="sliModule" [source]="source"></orll-ecsd-list>
		}
	}

	<ng-template #sharedContent>
		<div class="tab-content-container">
			<div class="row row-gap">
				<div class="iata-shipper-box orll-sli-shipper__box">
					<orll-sli-shipper #sliShipper [shipperInfo]="shipperInfo"></orll-sli-shipper>
				</div>
				<div class="iata-shipper-box orll-sli-consignee__box">
					<orll-sli-consignee #sliConsignee title="consignee" [shipmentParty]="consigneeInfo"></orll-sli-consignee>
				</div>
			</div>

			<div class="row">
				<div class="iata-shipper-box orll-sli-also-notify__box col-12">
					@for (alsoNotify of alsoNotifies; track $index) {
						<mat-expansion-panel class="orll-sli-also-notify__panel" [expanded]="true">
							<mat-expansion-panel-header>
								<mat-panel-title>
									<h2 class="mat-display-2 orll-sli-also-notify__title">
										{{ 'sli.mgmt.company.alsoNotify' | translate }}
									</h2>
								</mat-panel-title>
								<mat-panel-description>
									@if (alsoNotify.id) {
										<button
											[orllCopy]="alsoNotify.id"
											mat-icon-button
											color="primary"
											(click)="$event.preventDefault(); $event.stopPropagation()"
											class="orll-sli-also-notify__delete-button">
											<mat-icon>content_copy</mat-icon>
										</button>
									}
									<button
										mat-icon-button
										color="primary"
										(click)="delAlsoNotify($index, $event)"
										class="orll-sli-also-notify__delete-button">
										<mat-icon>delete</mat-icon>
									</button>
									<button
										mat-icon-button
										color="primary"
										(click)="getOrgList($index, $event)"
										class="orll-sli-also-notify__contact-button">
										<mat-icon>contacts</mat-icon>
									</button>
								</mat-panel-description>
							</mat-expansion-panel-header>
							<orll-sli-consignee #sliAlsoNotify [shipmentParty]="alsoNotify"></orll-sli-consignee>
						</mat-expansion-panel>
					}

					<div class="orll-sli-also-notify__footer">
						<button
							mat-stroked-button
							color="primary"
							type="button"
							(click)="addAlsoNotify()"
							class="orll-sli-also-notify__add-button">
							<mat-icon>add</mat-icon>
							{{ 'sli.mgmt.company.alsoNotify' | translate }}
						</button>
					</div>
				</div>
			</div>

			<div class="row margin-r-5">
				<div class="iata-shipper-box orll-sli-routing__box col-12">
					<orll-sli-routing #sliRouting></orll-sli-routing>
				</div>
			</div>
			<div class="row">
				<div class="iata-shipper-box orll-sli-shipper__box">
					<form [formGroup]="sliForm">
						<div class="row">
							<mat-form-field appearance="outline" class="width-100" floatLabel="always">
								<mat-label>{{ 'sli.mgmt.pieceList.goodsDescription' | translate }}</mat-label>
								<textarea matInput formControlName="goodsDescription" rows="4" required></textarea>
								@if (sliForm.get('goodsDescription')?.hasError('required')) {
									<mat-error>{{ 'sli.mgmt.pieceList.goodsDescription.required' | translate }}</mat-error>
								}
							</mat-form-field>
						</div>
						<div class="row margin-auto">
							<div class="width-10">
								<mat-form-field appearance="outline" floatLabel="always">
									<mat-label>{{ 'sli.mgmt.pieceList.declaredValueForCustoms' | translate }}</mat-label>
									<input matInput formControlName="declaredValueForCustoms" />
									@if (sliForm.get('declaredValueForCustoms')?.hasError('pattern')) {
										<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber2NCV' | translate }}</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="col-1 declared-value">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<input
										type="text"
										matInput
										formControlName="declaredValueForCustomsCurrency"
										[matAutocomplete]="autoCustomsCurrency" />
									<mat-autocomplete #autoCustomsCurrency="matAutocomplete">
										@for (currency of filteredDeclaredCustomsCurrency; track currency) {
											<mat-option [value]="currency">{{ currency }}</mat-option>
										}
									</mat-autocomplete>
									<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
								</mat-form-field>
							</div>

							<div class="width-10">
								<mat-form-field appearance="outline" floatLabel="always">
									<mat-label>{{ 'sli.mgmt.pieceList.declaredValueForCarriage' | translate }}</mat-label>
									<input matInput formControlName="declaredValueForCarriage" />
									@if (sliForm.get('declaredValueForCarriage')?.hasError('pattern')) {
										<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber2NVD' | translate }}</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="col-1 declared-value">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<input
										type="text"
										matInput
										formControlName="declaredValueForCarriageCurrency"
										[matAutocomplete]="autoCarriageCurrency" />
									<mat-autocomplete #autoCarriageCurrency="matAutocomplete">
										@for (currency of filteredDeclaredCarriageCurrency; track currency) {
											<mat-option [value]="currency">{{ currency }}</mat-option>
										}
									</mat-autocomplete>
									<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
								</mat-form-field>
							</div>
							<div class="width-10">
								<mat-form-field appearance="outline" floatLabel="always">
									<mat-label>{{ 'sli.mgmt.pieceList.insuredAmount' | translate }}</mat-label>
									<input matInput formControlName="insuredAmount" />
									@if (sliForm.get('insuredAmount')?.hasError('pattern')) {
										<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber2NIL' | translate }}</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="col-1 declared-value">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<input
										type="text"
										matInput
										formControlName="insuredAmountCurrency"
										[matAutocomplete]="autoInsuredAmountCurrency" />
									<mat-autocomplete #autoInsuredAmountCurrency="matAutocomplete">
										@for (currency of filteredInsuredAmountCurrency; track currency) {
											<mat-option [value]="currency">{{ currency }}</mat-option>
										}
									</mat-autocomplete>
									<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
								</mat-form-field>
							</div>
							<div class="width-13 total-gross-weight">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<mat-label>{{ 'sli.mgmt.pieceList.totalGrossWeight' | translate }}</mat-label>
									<input matInput formControlName="totalGrossWeight" required />
									<span matSuffix class="unit">KG</span>
									@if (sliForm.get('totalGrossWeight')?.hasError('required')) {
										<mat-error>{{ 'sli.mgmt.pieceList.totalGrossWeight.required' | translate }}</mat-error>
									}
									@if (sliForm.get('totalGrossWeight')?.hasError('pattern')) {
										<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="width-10 total-dimensions">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<mat-label>{{ 'sli.mgmt.pieceList.totalDimensions' | translate }}</mat-label>
									<input
										matInput
										formControlName="dimLength"
										placeholder="{{ 'sli.mgmt.pieceList.dimLength' | translate }}"
										required />
									<span matSuffix class="unit">CM</span>
									@if (sliForm.get('dimLength')?.hasError('required')) {
										<mat-error>{{ 'sli.mgmt.pieceList.dimLength.required' | translate }}</mat-error>
									}
									@if (sliForm.get('dimLength')?.hasError('pattern')) {
										<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="width-10 total-dimensions">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<input
										matInput
										formControlName="dimWidth"
										placeholder="{{ 'sli.mgmt.pieceList.dimWidth' | translate }}"
										required />
									<span matSuffix class="unit">CM</span>
									@if (sliForm.get('dimWidth')?.hasError('required')) {
										<mat-error>{{ 'sli.mgmt.pieceList.dimWidth.required' | translate }}</mat-error>
									}
									@if (sliForm.get('dimWidth')?.hasError('pattern')) {
										<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="width-10 total-dimensions">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<input
										matInput
										formControlName="dimHeight"
										placeholder="{{ 'sli.mgmt.pieceList.dimHeight' | translate }}"
										required />
									<span matSuffix class="unit">CM</span>
									@if (sliForm.get('dimHeight')?.hasError('required')) {
										<mat-error>{{ 'sli.mgmt.pieceList.dimHeight.required' | translate }}</mat-error>
									}
									@if (sliForm.get('dimHeight')?.hasError('pattern')) {
										<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
									}
								</mat-form-field>
							</div>
						</div>
						<div class="row">
							<div class="fill">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<mat-label>{{ 'sli.mgmt.pieceList.textualHandlingInstructions' | translate }}</mat-label>
									<textarea matInput formControlName="textualHandlingInstructions" rows="4"></textarea>
								</mat-form-field>
							</div>
							<div class="col-2">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<mat-label>{{ 'sli.mgmt.pieceList.weightValuationIndicator' | translate }}</mat-label>
									<mat-select formControlName="weightValuationIndicator">
										@for (wvi of weightValuationIndicators; track wvi) {
											<mat-option [value]="wvi">{{ wvi }}</mat-option>
										}
									</mat-select>
								</mat-form-field>

								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<mat-label>{{ 'sli.mgmt.pieceList.incoterms' | translate }}</mat-label>
									<mat-select formControlName="incoterms">
										@for (incoterm of incoterms; track incoterm.code) {
											<mat-option [value]="incoterm.code">{{ incoterm.name }}</mat-option>
										}
									</mat-select>
								</mat-form-field>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
		<div class="orll-sli-create-page__footer">
			<button mat-stroked-button color="primary" (click)="onCancel()" class="orll-sli-create-page__cancel-button">
				{{ 'sli.mgmt.cancel' | translate }}
			</button>
			@if (!disableUpdate && (hasPermission(savePermission, sliModule) | async)) {
				<button mat-flat-button color="primary" (click)="onSave()">
					<mat-icon>save</mat-icon>
					{{ 'sli.mgmt.save' | translate }}
				</button>
			}
		</div>
	</ng-template>
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
