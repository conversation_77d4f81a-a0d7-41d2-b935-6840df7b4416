@if (bookingOptionDetail?.bookingOptionList?.length === 0) {
	<orll-dialog class="booking-option-dialog">
		<ng-container dialog-title>
			{{ 'booking.dialog.title.option' | translate }}
		</ng-container>
		<div dialog-content class="booking-option-dialog__content">
			<div class="booking-option-dialog__request-detail">
				<orll-booking-option-request-detail [detailInfo]="bookingRequestDetail"></orll-booking-option-request-detail>
			</div>
			@if (hasSomeRole(forwarder) | async) {
				<div class="booking-option-dialog__share-history">
					<div class="booking-option-dialog__share-history-title">
						{{ 'booking.option.share.history' | translate }}
					</div>

					<div class="booking-option-dialog__share-history-table">
						<div class="row table-title">
							<div class="col-6">
								{{ 'booking.option.request.to' | translate }}
							</div>
							<div class="col-6">
								{{ 'booking.option.request.date' | translate }}
							</div>
						</div>
						@if (bookingOptionDetail?.shareList) {
							@for (shareInfo of bookingOptionDetail.shareList; track shareInfo.orgId) {
								<div class="row">
									<div class="col-6">
										{{ shareInfo.airlineCode ?? '' }}
									</div>
									<div class="col-6">
										{{ shareInfo.requestTime ?? '' }}
									</div>
								</div>
							}
						}
					</div>
				</div>
			}
		</div>
		<ng-container dialog-actions>
			<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary">
				{{ 'common.dialog.cancel' | translate }}
			</button>
		</ng-container>
	</orll-dialog>
} @else {
	<orll-dialog class="booking-option-dialog">
		<ng-container dialog-title>
			{{ 'booking.dialog.title.option' | translate }}
		</ng-container>
		<div dialog-content class="booking-option-dialog__content">
			<div class="booking-option-dialog__request-detail">
				<orll-booking-option-request-detail [quote]="true" [detailInfo]="bookingRequestDetail"></orll-booking-option-request-detail>
			</div>

			@if (hasSomeRole(forwarder) | async) {
				@for (bookingOption of bookingAirlineDetails; track bookingOption.id) {
					<orll-booking-transport
						[bookingAirlineDetail]="bookingOption"
						[selectable]="true"
						(selectedOption)="setBookingSelectedOption($event, bookingOption)"></orll-booking-transport>
				}

				@if (data.latestStatus === quoteProvidedStatus) {
					<form [formGroup]="mawbForm">
						<div class="row gutters-x-3 mawb">
							<div class="col-2">
								<mat-form-field class="width-full">
									<mat-label>{{ 'mawb.formItem.mawbPrefix' | translate }}</mat-label>
									<mat-select formControlName="mawbPrefix">
										@for (carrier of carriers; track carrier.id) {
											<mat-option [value]="carrier.id">{{ carrier.prefix }}</mat-option>
										}
									</mat-select>
								</mat-form-field>
							</div>
							<div class="col-2">
								<mat-form-field class="width-full margin-b-5">
									<mat-label>{{ 'mawb.formItem.mawbNumber' | translate }}</mat-label>
									<input matInput formControlName="mawbNumber" />
									@if (mawbForm.get('mawbNumber')?.hasError('mawbNumberFormat')) {
										<mat-error>{{ mawbForm.get('mawbNumber')?.errors?.['mawbNumberFormat']?.message }}</mat-error>
									}
									@if (mawbForm.get('mawbNumber')?.hasError('mawbNumberCheckDigit')) {
										<mat-error>{{ mawbForm.get('mawbNumber')?.errors?.['mawbNumberCheckDigit']?.message }}</mat-error>
									}
								</mat-form-field>
							</div>
						</div>
					</form>
				}
			}
		</div>

		<ng-container dialog-actions>
			<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary">
				{{ 'common.dialog.cancel' | translate }}
			</button>
			@if (hasSomeRole(forwarder) | async) {
				<button mat-flat-button color="primary" [disabled]="!selectedOption" (click)="createBooking()">
					<mat-icon>send</mat-icon>

					{{ 'booking.option.request.booking' | translate }}
				</button>
			}
		</ng-container>
	</orll-dialog>
}

@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
