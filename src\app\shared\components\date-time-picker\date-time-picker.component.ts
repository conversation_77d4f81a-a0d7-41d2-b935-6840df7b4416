import { DatePipe } from '@angular/common';
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	Component,
	ElementRef,
	EventEmitter,
	forwardRef,
	Input,
	OnInit,
	Output,
	ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ControlValueAccessor, FormControl, FormsModule, ReactiveFormsModule, ValidatorFn, NG_VALUE_ACCESSOR } from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepicker, MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule, MatError } from '@angular/material/form-field';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { DATE_TIME_FORMAT, INPUT_DATE_TIME_FORMAT, TIME_FORMAT } from '@shared/models/constant';
import { DateTime } from 'luxon';
import { RolesAwareComponent } from '../roles-aware/roles-aware.component';
import { MatTooltip } from '@angular/material/tooltip';
import { MatIconButton } from '@angular/material/button';

const TIME_DEFAULT = '00:00';

@Component({
	selector: 'orll-date-time-picker',
	imports: [
		MatDatepickerModule,
		MatFormFieldModule,
		MatError,
		MatIconModule,
		MatIcon,
		MatIconButton,
		MatTooltip,
		MatInputModule,
		TranslateModule,
		FormsModule,
		ReactiveFormsModule,
	],
	providers: [
		provideNativeDateAdapter(),
		DatePipe,
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => DateTimePickerComponent),
			multi: true,
		},
	],
	templateUrl: './date-time-picker.component.html',
	styleUrl: './date-time-picker.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DateTimePickerComponent extends RolesAwareComponent implements OnInit, AfterViewInit, ControlValueAccessor {
	@Input() label = '';
	@Input() value: DateTime | null = null;
	@Input() validatorFn: ValidatorFn | null = null;
	@Output() valueChange = new EventEmitter<DateTime | null>();

	@ViewChild('picker') picker!: MatDatepicker<Date>;

	selectedDate: DateTime | null = null;
	displayValue: string | null = null;
	selectedTime = TIME_DEFAULT;
	selection = new FormControl({ disabled: false });

	// eslint-disable-next-line @typescript-eslint/no-empty-function
	private onChange: (value: DateTime | null) => void = () => {};
	// eslint-disable-next-line @typescript-eslint/no-empty-function
	private onTouched: () => void = () => {};
	@ViewChild('pickerInput', { read: ElementRef }) pickerInput!: ElementRef<HTMLInputElement>;

	ngOnInit() {
		this.updateDisplayValue();
	}

	ngAfterViewInit(): void {
		this.picker.closedStream.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
			if (!this.selectedDate) {
				this.picker.open();
			}
		});
		this.updateDisplayValue();
	}

	writeValue(value: DateTime | null): void {
		if (typeof value === 'string' && value) {
			let inputDate = DateTime.fromFormat(value, DATE_TIME_FORMAT);
			if (!inputDate.isValid) {
				inputDate = DateTime.fromISO(value);
			}
			value = inputDate;
		}
		this.selectedDate = value;
		this.selectedTime = value ? value.toFormat(TIME_FORMAT) : TIME_DEFAULT;
		this.updateDisplayValue();
	}

	eraseValue(event: Event): void {
		event.preventDefault();
		event.stopPropagation();

		this.selectedDate = null;
		this.selectedTime = TIME_DEFAULT;
		this.updateDisplayValue();
	}

	onDateChange(date: Date | null) {
		this.selectedDate = date ? DateTime.fromJSDate(date) : DateTime.now();
		// 用户选择日期后标记为 touched
		if (!this.selection.touched) {
			this.selection.markAsTouched();
		}
		this.updateDisplayValue();
	}

	onTimeChange(event: Event) {
		this.selectedTime = (event.target as HTMLInputElement).value;
		// 用户选择时间后标记为 touched
		if (!this.selection.touched) {
			this.selection.markAsTouched();
		}
		this.picker._applyPendingSelection();
		this.updateDisplayValue();
	}

	private updateDisplayValue() {
		if (!this.selectedDate) {
			this.displayValue = null;
			if (this.pickerInput?.nativeElement) {
				this.pickerInput.nativeElement.value = '';
				const event = new Event('input', { bubbles: true });
				this.pickerInput.nativeElement.dispatchEvent(event);
			}
		} else {
			const [hour, minute] = this.selectedTime.split(':').map(Number);
			const combined = this.selectedDate.set({ hour, minute });
			this.displayValue = combined.toFormat(INPUT_DATE_TIME_FORMAT);

			if (this.pickerInput?.nativeElement) {
				this.pickerInput.nativeElement.value = this.displayValue;
				const event = new Event('input', { bubbles: true });
				this.pickerInput.nativeElement.dispatchEvent(event);
			}
		}

		this.emitValue();
	}

	private emitValue() {
		let valueToEmit: DateTime | null = null;

		if (this.selectedDate) {
			const [hour, minute] = this.selectedTime.split(':').map(Number);
			valueToEmit = this.selectedDate.set({ hour, minute });
		}

		this.valueChange.emit(valueToEmit);
		this.onChange(valueToEmit);
		this.onTouched();
	}

	private setValidators(markAsTouched = false): void {
		if (this.validatorFn) {
			if (markAsTouched) {
				this.selection.markAsTouched();
			}
			this.selection.setValidators(this.validatorFn);
			this.selection.updateValueAndValidity();
		}
	}

	registerOnChange(fn: any): void {
		this.onChange = fn;
		// 初始化时不标记为 touched，避免立即显示验证错误
		this.setValidators(false);
	}

	registerOnTouched(fn: any): void {
		this.onTouched = fn;
	}

	setDisabledState(disable: boolean): void {
		if (disable) {
			this.selection.disable();
			return;
		}
		this.selection.enable();
	}

	onBlur() {
		// 用户交互后标记为 touched，这样验证错误才会显示
		if (!this.selection.touched) {
			this.selection.markAsTouched();
		}
		this.onTouched();
	}

	/**
	 * 手动标记组件为 touched 状态，用于父组件调用 markAllAsTouched() 后触发验证显示
	 */
	markAsTouched(): void {
		this.selection.markAsTouched();
	}
}
