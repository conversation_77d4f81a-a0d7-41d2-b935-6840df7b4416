import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, Routes, withComponentInputBinding } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { UserRole } from '@shared/models/user-role.model';
import { CanDeactivateGuard } from '@shared/services/can-deactivate.guard';
import { RouteGuardService } from '@shared/services/route-guard.service';

const MENU_SLI_ROLES: string[] = [UserRole.SHIPPER, UserRole.FORWARDER];
const MENU_HAWB_ROLES: string[] = [UserRole.FORWARDER, UserRole.CARRIER, UserRole.SHIPPER];
const MENU_MAWB_ROLES: string[] = [UserRole.FORWARDER, UserRole.CARRIER, UserRole.AGENT, UserRole.CUSTOM, UserRole.SECURITY];
const MENU_CHANGE_REQUEST_ROLES: string[] = [UserRole.SHIPPER, UserRole.FORWARDER, UserRole.CARRIER, UserRole.AGENT];
const MENU_BOOKING_ROLES: string[] = [UserRole.FORWARDER, UserRole.CARRIER];

export const ROUTES: Routes = [
	{
		path: 'dashboard',
		data: {
			breadcrumb: {
				label: 'home.dashboard',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/dashboard/pages/home-dashboard/home-dashboard.component'),
				canActivate: [MsalGuard],
			},
		],
	},

	{
		path: 'sli',
		data: {
			breadcrumb: {
				label: 'sli.mgmt.list',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/sli-mgmt/pages/sli-list/sli-list-page.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_SLI_ROLES)],
			},
			{
				path: 'create/:tabIndex',
				data: {
					breadcrumb: {
						label: 'sli.mgmt.create',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-create/sli-create-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_SLI_ROLES)],
					},
					{
						path: 'piece/:pieceType',
						loadComponent: () => import('./modules/sli-mgmt/pages/piece-add/piece-add-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_SLI_ROLES)],
						data: {
							breadcrumb: {
								label: 'sli.piece.add',
							},
						},
					},
					{
						path: 'piece/:pieceType/:pieceId',
						loadComponent: () => import('./modules/sli-mgmt/pages/piece-add/piece-add-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_SLI_ROLES)],
						data: {
							breadcrumb: {
								label: 'sli.piece.edit',
							},
						},
					},
				],
			},
			{
				path: 'edit/:sliNumber/:tabIndex',
				data: {
					breadcrumb: {
						label: 'sli.mgmt.edit',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-create/sli-create-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_SLI_ROLES)],
					},
					{
						path: 'piece/:pieceType',
						loadComponent: () => import('./modules/sli-mgmt/pages/piece-add/piece-add-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_SLI_ROLES)],
						data: {
							breadcrumb: {
								label: 'sli.piece.add',
							},
						},
					},
					{
						path: 'piece/:pieceType/:pieceId',
						loadComponent: () => import('./modules/sli-mgmt/pages/piece-add/piece-add-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_SLI_ROLES)],
						data: {
							breadcrumb: {
								label: 'sli.piece.edit',
							},
						},
					},
				],
			},
		],
	},

	{
		path: 'hawb',
		data: {
			breadcrumb: {
				label: 'hawb.mgmt.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/hawb-mgmt/pages/hawb-list/hawb-list-page.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_HAWB_ROLES)],
			},
			{
				path: 'edit/:hawbId',
				data: {
					breadcrumb: {
						label: 'hawb.mgmt.edit',
					},
				},
				loadComponent: () => import('./modules/hawb-mgmt/pages/create-hawb-from-shared-sli/create-hawb-from-shared-sli.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_HAWB_ROLES)],
			},

			{
				path: 'create',
				data: {
					breadcrumb: {
						label: 'hawb.mgmt.create.fromSli',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-list/sli-list-page.component'),
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_HAWB_ROLES)],
						data: {
							fromCreateHawb: true,
						},
					},
					{
						path: ':fromSli/calculate',
						data: {
							breadcrumb: {
								label: 'calculate.title',
							},
						},
						loadComponent: () => import('./shared/components/weight-calculate/weight-calculate.component'),
						providers: [withComponentInputBinding()],
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_HAWB_ROLES)],
					},
					{
						path: ':fromSli/detail',
						data: {
							breadcrumb: {
								label: 'hawb.mgmt.create.fromSliDetail',
							},
						},
						loadComponent: () =>
							import('./modules/hawb-mgmt/pages/create-hawb-from-shared-sli/create-hawb-from-shared-sli.component'),
						resolve: {
							sliNumber: (route: ActivatedRouteSnapshot) => route.paramMap.get('fromSli'),
						},
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_HAWB_ROLES)],
					},
				],
			},

			{
				path: 'status-tracking',
				loadComponent: () =>
					import('./modules/hawb-mgmt/pages/status-tracking/status-tracking.component').then((it) => it.StatusTrackingComponent),
			},
		],
	},

	{
		path: 'mawb',
		data: {
			breadcrumb: {
				label: 'mawb.mgmt.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/mawb-mgmt/pages/mawb-list/mawb-list.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_MAWB_ROLES)],
			},
			{
				path: 'edit/:mawbId',
				data: {
					breadcrumb: {
						label: 'mawb.mgmt.edit',
					},
				},
				loadComponent: () => import('./modules/mawb-mgmt/pages/mawb-create/create-mawb-from-hawb.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_MAWB_ROLES)],
			},

			{
				path: 'create',
				data: {
					breadcrumb: {
						label: 'mawb.mgmt.create.fromHawb',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/hawb-mgmt/pages/hawb-list/hawb-list-page.component'),
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_MAWB_ROLES)],
						data: {
							fromCreateMawb: true,
						},
					},
					{
						path: 'detail',
						data: {
							breadcrumb: {
								label: 'mawb.mgmt.create.fromHawbDetail',
							},
						},
						loadComponent: () => import('./modules/mawb-mgmt/pages/mawb-create/create-mawb-from-hawb.component'),
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_MAWB_ROLES)],
					},
					{
						path: 'calculate',
						data: {
							breadcrumb: {
								label: 'calculate.title',
							},
						},
						loadComponent: () => import('./shared/components/weight-calculate/weight-calculate.component'),
						canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_MAWB_ROLES)],
					},
				],
			},
		],
	},

	{
		path: 'change-requests',
		data: {
			breadcrumb: {
				label: 'common.mainHeader.mainNav.changeRequests',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () =>
					import('./shared/components/biz-logic/version-history/change-request-list/change-request-list.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_CHANGE_REQUEST_ROLES)],
			},
		],
	},

	{
		path: 'partner',
		data: {
			breadcrumb: {
				label: 'common.mainHeader.mainNav.partner',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/partner-access/pages/partner-list/partner-list.component'),
				canActivate: [MsalGuard],
			},
		],
	},
	{
		path: 'notification',
		data: {
			breadcrumb: {
				label: 'notifications.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/notification/pages/notification-list/notification-list.component'),
				canActivate: [MsalGuard],
			},
		],
	},
	{
		path: 'subscription',
		data: {
			breadcrumb: {
				label: 'subscription.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/subscription/pages/subscription-list/subscription-list.component'),
				canActivate: [MsalGuard],
			},
		],
	},

	{
		path: 'users-mgmt',
		data: {
			breadcrumb: {
				label: 'users.mgmt.list',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/user-mgmt/pages/user-list/user-list-page.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).isSuperUser()],
			},
		],
	},
	{
		path: 'rule-mgmt',
		data: {
			breadcrumb: {
				label: 'system.rule.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/sys-mgmt/pages/rule-list/rule-list.component'),
				canActivate: [MsalGuard],
			},
		],
	},
	{
		path: 'server-mgmt',
		data: {
			breadcrumb: {
				label: 'system.server.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/server-mgmt/pages/server-management/server-management.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).isSuperUser()],
			},
		],
	},
	{
		path: 'quote',
		data: {
			breadcrumb: {
				label: 'common.mainHeader.mainNav.distribution.quote',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/booking/pages/booking-option-list/booking-option-request-list.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_BOOKING_ROLES)],
			},
			{
				path: 'option',
				data: {
					breadcrumb: {
						label: 'booking.option.add.booking.option.btn',
					},
				},
				loadComponent: () => import('./modules/booking/components/booking-option-create/booking-option-create.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_BOOKING_ROLES)],
			},
		],
	},

	{
		path: 'booking',
		data: {
			breadcrumb: {
				label: 'common.mainHeader.mainNav.distribution.booking',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/booking/pages/booking-request-list/booking-request-list.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_BOOKING_ROLES)],
			},
			{
				path: 'create',
				data: {
					breadcrumb: {
						label: 'booking.mgmt.create',
					},
				},
				loadComponent: () => import('./modules/booking/components/booking-create/booking-create.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole([UserRole.FORWARDER])],
			},
		],
	},

	// Role-based default redirect
	{
		path: '',
		loadComponent: () => import('./shared/components/redirect/redirect.component').then((m) => m.default),
		canActivate: [MsalGuard],
		pathMatch: 'full',
	},
	{ path: '**', redirectTo: '' },
] as Routes;
