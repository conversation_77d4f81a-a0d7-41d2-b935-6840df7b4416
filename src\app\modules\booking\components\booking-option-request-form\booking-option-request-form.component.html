<div class="booking-option-request-form">
	<form [formGroup]="bookingOptionReqForm">
		<div class="row booking-option-request-form__sub-title">
			{{ 'booking.option.subTitle.booking.shipment' | translate }}
		</div>
		<div class="row">
			<mat-form-field appearance="outline" class="width-20" floatLabel="always">
				<mat-label>{{ 'sli.piece.table.column.pieceQuantity' | translate }}</mat-label>
				<input matInput formControlName="pieceGroupCount" />
				@if (bookingOptionReqForm.get('pieceGroupCount')?.hasError('required')) {
					<mat-error>{{
						'validators.required' | translate: { field: 'sli.piece.table.column.pieceQuantity' | translate }
					}}</mat-error>
				}
			</mat-form-field>
			<mat-form-field appearance="outline" class="width-20" floatLabel="always">
				<mat-label>{{ 'sli.piece.grossWeight' | translate }}</mat-label>
				<input matInput formControlName="totalGrossWeight" />
				<span matSuffix class="unit">KG</span>
				@if (bookingOptionReqForm.get('totalGrossWeight')?.hasError('required')) {
					<mat-error>{{
						'validators.required' | translate: { field: 'booking.request.totalGrossWeight' | translate }
					}}</mat-error>
				}
			</mat-form-field>
			<mat-form-field appearance="outline" class="width-20" floatLabel="always">
				<mat-label>{{ 'booking.option.chargeable.weight' | translate }}</mat-label>
				<input matInput formControlName="chargeableWeight" (change)="chargeableWeightChange.emit($event.target.value)" />
				<span matSuffix class="unit">KG</span>
				@if (bookingOptionReqForm.get('chargeableWeight')?.hasError('required')) {
					<mat-error>{{
						'validators.required' | translate: { field: 'booking.option.chargeable.weight' | translate }
					}}</mat-error>
				}
			</mat-form-field>
			<div class="width-35 d-inline-flex dimensions align-items-center">
				<div class="lwh">
					<mat-form-field appearance="outline" class="width-100" floatLabel="always">
						<mat-label>{{ 'sli.piece.dimensions' | translate }}</mat-label>
						<input matInput formControlName="dimLength" placeholder="{{ 'sli.mgmt.pieceList.dimLength' | translate }}" />
						<span matSuffix class="unit">CM</span>
						@if (bookingOptionReqForm.get('dimLength')?.hasError('pattern')) {
							<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="lwh">
					<mat-form-field appearance="outline" class="width-100" floatLabel="always">
						<input matInput formControlName="dimWidth" placeholder="{{ 'sli.mgmt.pieceList.dimWidth' | translate }}" />
						<span matSuffix class="unit">CM</span>
						@if (bookingOptionReqForm.get('dimWidth')?.hasError('pattern')) {
							<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="lwh">
					<mat-form-field appearance="outline" class="width-100" floatLabel="always">
						<input matInput formControlName="dimHeight" placeholder="{{ 'sli.mgmt.pieceList.dimHeight' | translate }}" />
						<span matSuffix class="unit">CM</span>
						@if (bookingOptionReqForm.get('dimHeight')?.hasError('pattern')) {
							<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber1' | translate }}</mat-error>
						}
					</mat-form-field>
				</div>
			</div>
		</div>
		<div class="row">
			<mat-form-field appearance="outline" class="width-20" floatLabel="always">
				<mat-label>{{ 'booking.option.commodity.code' | translate }}</mat-label>
				<mat-select formControlName="expectedCommodity">
					@for (code of commodityCodeList; track code.code) {
						<mat-option [value]="code.code">{{ code.name }}</mat-option>
					}
				</mat-select>
				@if (bookingOptionReqForm.get('expectedCommodity')?.hasError('required')) {
					<mat-error>{{ 'validators.required' | translate: { field: 'booking.option.commodity.code' | translate } }}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="width-20" floatLabel="always">
				<mat-label>{{ 'booking.option.handling.code' | translate }}</mat-label>
				<mat-select formControlName="specialHandlingCodes" multiple="true">
					@for (code of handlingCodeList; track code.code) {
						<mat-option [value]="code.code">{{ code.name }}</mat-option>
					}
				</mat-select>
				@if (bookingOptionReqForm.get('specialHandlingCodes')?.hasError('required')) {
					<mat-error>{{ 'validators.required' | translate: { field: 'booking.option.handling.code' | translate } }}</mat-error>
				}
			</mat-form-field>
			<mat-form-field appearance="outline" class="width-55" floatLabel="always">
				<mat-label>{{ 'booking.option.handling.instruction' | translate }}</mat-label>
				<input matInput formControlName="textualHandlingInstructions" />
				@if (bookingOptionReqForm.get('textualHandlingInstructions')?.hasError('required')) {
					<mat-error>{{
						'validators.required' | translate: { field: 'booking.option.handling.instruction' | translate }
					}}</mat-error>
				}
			</mat-form-field>
		</div>

		@if (!fromBooking) {
			<div class="row booking-option-request-form__sub-title">
				{{ 'booking.option.subTitle.itinerary' | translate }}
			</div>
			<div class="row">
				<mat-form-field class="width-20">
					<mat-label>{{ 'booking.option.departure.location' | translate }}</mat-label>
					<mat-select formControlName="departureLocation">
						@for (airline of locationList; track airline.code) {
							<mat-option [value]="airline">{{ airline.name }}</mat-option>
						}
					</mat-select>

					@if (bookingOptionReqForm.get('departureLocation')?.hasError('required')) {
						<mat-error>{{
							'validators.required' | translate: { field: 'booking.option.departure.location' | translate }
						}}</mat-error>
					}
				</mat-form-field>
				<mat-form-field class="width-20">
					<mat-label>{{ 'booking.option.arrival.location' | translate }}</mat-label>
					<mat-select formControlName="arrivalLocation">
						@for (airline of locationList; track airline.code) {
							<mat-option [value]="airline">{{ airline.name }}</mat-option>
						}
					</mat-select>
					@if (bookingOptionReqForm.get('arrivalLocation')?.hasError('required')) {
						<mat-error>{{
							'validators.required' | translate: { field: 'booking.option.departure.location' | translate }
						}}</mat-error>
					}
				</mat-form-field>
				<mat-form-field appearance="outline" class="width-20" floatLabel="always">
					<mat-label>{{ 'booking.option.max.segment' | translate }}</mat-label>
					<input matInput formControlName="maxSegments" />
				</mat-form-field>
				<mat-form-field appearance="outline" class="width-20" floatLabel="always">
					<mat-label>{{ 'booking.option.transport.id' | translate }}</mat-label>
					<input matInput formControlName="preferredTransportId" />
				</mat-form-field>
			</div>
		}

		<div class="row booking-option-request-form__sub-title">
			{{ 'booking.option.subTitle.preference' | translate }}
		</div>
		@if (!fromBooking) {
			<div class="row">
				<div class="width-20">
					<orll-date-time-picker
						[label]="'booking.option.earliest.acceptance.time'"
						formControlName="earliestAcceptanceTime"></orll-date-time-picker>
				</div>
				<div class="width-20">
					<orll-date-time-picker
						[label]="'booking.option.latest.acceptance.time'"
						formControlName="latestAcceptanceTime"></orll-date-time-picker>
				</div>
				<div class="width-20">
					<orll-date-time-picker
						[label]="'booking.option.latest.arrival.time'"
						formControlName="latestArrivalTime"></orll-date-time-picker>
				</div>
				<div class="width-20">
					<orll-date-time-picker
						[label]="'booking.option.shipment.available.date'"
						formControlName="timeOfAvailability"></orll-date-time-picker>
				</div>
			</div>
		}
		<div class="row">
			<mat-form-field class="width-20">
				<mat-label>{{ 'booking.option.currency' | translate }}</mat-label>
				<mat-select formControlName="currency">
					@for (currency of currencyList; track currency) {
						<mat-option [value]="currency">{{ currency }}</mat-option>
					}
				</mat-select>
			</mat-form-field>
			@if (fromBooking) {
				<mat-form-field class="width-20">
					<mat-label>{{ 'mawb.formItem.mawbPrefix' | translate }}</mat-label>
					<mat-select formControlName="mawbPrefix">
						@for (carrier of carriers; track carrier.id) {
							<mat-option [value]="carrier.prefix">{{ carrier.prefix }}</mat-option>
						}
					</mat-select>
				</mat-form-field>
				<mat-form-field class="width-20">
					<mat-label>{{ 'mawb.formItem.mawbNumber' | translate }}</mat-label>
					<input matInput formControlName="mawbNumber" />
					@if (bookingOptionReqForm.get('mawbNumber')?.hasError('mawbNumberFormat')) {
						<mat-error>{{ bookingOptionReqForm.get('mawbNumber')?.errors?.['mawbNumberFormat']?.message }}</mat-error>
					}
					@if (bookingOptionReqForm.get('mawbNumber')?.hasError('mawbNumberCheckDigit')) {
						<mat-error>{{ bookingOptionReqForm.get('mawbNumber')?.errors?.['mawbNumberCheckDigit']?.message }}</mat-error>
					}
				</mat-form-field>
			}
		</div>
	</form>
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
