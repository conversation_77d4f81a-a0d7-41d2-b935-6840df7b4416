import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChildren,
	QueryList,
} from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DATE_TIME_FORMAT, DEFAULT_WEIGHT_UNIT, REGX_NUMBER_1_DECIMAL, REGX_POSITIVE_NUMBER } from '@shared/models/constant';
import { BookingInfo, BookingOptionRequestDetailObj } from '../../models/booking.model';
import { MatInputModule } from '@angular/material/input';
import { DateTimePickerComponent } from '@shared/components/date-time-picker/date-time-picker.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatExpansionModule } from '@angular/material/expansion';
import { DateTime } from 'luxon';
import { BookingOptionRequestService } from '../../services/booking-option-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { forkJoin } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { CodeType } from '@shared/models/search-type.model';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { Organization } from '@shared/models/organization.model';
import { OrgType } from '@shared/models/org-type.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';

@Component({
	selector: 'orll-booking-option-request-form',
	imports: [
		TranslateModule,
		MatDividerModule,
		ReactiveFormsModule,
		MatIconModule,
		MatButtonModule,
		MatInputModule,
		MatFormFieldModule,
		MatSelectModule,
		MatExpansionModule,
		DateTimePickerComponent,
		SpinnerComponent,
	],
	templateUrl: './booking-option-request-form.component.html',
	styleUrl: './booking-option-request-form.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BookingOptionRequestFormComponent extends RolesAwareComponent implements OnInit {
	@Input() bookingOptionRequestId: string | null = null;
	@Input() fromBooking = false;
	@Output() chargeableWeightChange = new EventEmitter<string>();

	@ViewChildren(DateTimePickerComponent)
	dateTimePickerComponents!: QueryList<DateTimePickerComponent>;

	bookingOptionReqForm: FormGroup = new FormGroup({
		totalGrossWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		chargeableWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimLength: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimWidth: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimHeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		pieceGroupCount: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_POSITIVE_NUMBER)]),
		expectedCommodity: new FormControl<string>('', [Validators.required]),
		specialHandlingCodes: new FormControl<string[] | null>(null),
		textualHandlingInstructions: new FormControl<string>(''),
		departureLocation: new FormControl<CodeName | null>(null, [Validators.required]),
		arrivalLocation: new FormControl<CodeName | null>(null, [Validators.required]),
		maxSegments: new FormControl<number | null>(null),
		preferredTransportId: new FormControl<string>(''),
		earliestAcceptanceTime: new FormControl<DateTime | null>(null),
		latestAcceptanceTime: new FormControl<DateTime | null>(null),
		latestArrivalTime: new FormControl<DateTime | null>(null),
		timeOfAvailability: new FormControl<DateTime | null>(null),
		currency: new FormControl<string>(''),
		mawbPrefix: new FormControl<string>(''),
		mawbNumber: new FormControl<string>('', [this.mawbNumberValidator.bind(this)]),
	});

	dataLoading = false;
	currencyList: string[] = [];
	commodityCodeList: CodeName[] = [];
	handlingCodeList: CodeName[] = [];
	locationList: CodeName[] = [];
	carriers: Organization[] = [];

	constructor(
		private readonly bookingService: BookingOptionRequestService,
		private readonly translateService: TranslateService,
		private readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	mawbNumberValidator(control: AbstractControl): ValidationErrors | null {
		const value = control.value;

		if (!value) {
			return null;
		}

		const numberPattern = /^\d{8}$/;
		if (!numberPattern.test(value)) {
			return { mawbNumberFormat: { message: this.translateService.instant('mawb.formItem.mawbNumber.checkLength') } };
		}

		const first7Digits = value.substring(0, 7);
		const checkDigit = Number.parseInt(value.charAt(7), 10);

		const calculatedCheckDigit = Number.parseInt(first7Digits, 10) % 7;

		if (checkDigit !== calculatedCheckDigit) {
			return {
				mawbNumberCheckDigit: {
					message: this.translateService.instant('mawb.formItem.mawbNumber.checkDigit') + calculatedCheckDigit,
				},
			};
		}

		return null;
	}

	ngOnInit() {
		forkJoin({
			currencies: this.bookingService.getCurrencies(),
			commodityCodes: this.bookingService.getCodeByType(CodeType.COMMODITY_CODE),
			handlingCodes: this.bookingService.getCodeByType(CodeType.HANDLING_CODE),
			carriers: this.orgMgmtRequestService.getOrgList(OrgType.CARRIER),
		})
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe(({ currencies, commodityCodes, handlingCodes, carriers }) => {
				this.currencyList = currencies;
				this.commodityCodeList = commodityCodes;
				this.handlingCodeList = handlingCodes;
				this.carriers = carriers;
				this.cdr.markForCheck();
			});
	}

	getFormData(): BookingOptionRequestDetailObj | BookingInfo {
		const value = this.bookingOptionReqForm.value;
		const bookingShipment = {
			id: '',
			pieceGroups: {
				id: '',
				pieceGroupCount: value.pieceGroupCount,
			},
			totalGrossWeight: { currencyUnit: DEFAULT_WEIGHT_UNIT, numericalValue: value.totalGrossWeight ?? 0 },
			chargeableWeight: { currencyUnit: DEFAULT_WEIGHT_UNIT, numericalValue: value.chargeableWeight ?? 0 },
			dimensions: {
				length: value.dimLength ?? 0,
				width: value.dimWidth ?? 0,
				height: value.dimHeight ?? 0,
			},
			expectedCommodity: value.expectedCommodity ?? '',
			specialHandlingCodes: value.specialHandlingCodes || null,
			textualHandlingInstructions: value.textualHandlingInstructions ?? '',
		};
		const unitsPreference = {
			currency: { currencyUnit: value.currency ?? '' },
		};
		let param: BookingOptionRequestDetailObj | BookingInfo;

		if (this.fromBooking) {
			param = {
				bookingShipment,
				unitsPreference,
				waybillPrefix: value.mawbPrefix ?? '',
				waybillNumber: value.mawbNumber ?? '',
			} as BookingInfo;
		} else {
			param = {
				bookingShipmentDetails: bookingShipment,
				transportLegs: [
					{
						departureLocation: {
							locationCodes: [
								{
									code: value.departureLocation?.code ?? '',
								},
							],
							locationName: value.departureLocation?.name ?? '',
						},
						arrivalLocation: {
							locationCodes: [
								{
									code: value.arrivalLocation?.code ?? '',
								},
							],
							locationName: value.arrivalLocation?.name ?? '',
						},
					},
				],
				bookingPreference: [
					{
						maxSegments: value.maxSegments,
						preferredTransportId: value.preferredTransportId,
					},
				],
				timePreferences: {
					earliestAcceptanceTime: value.earliestAcceptanceTime ? value.earliestAcceptanceTime.toFormat(DATE_TIME_FORMAT) : '',
					latestAcceptanceTime: value.latestAcceptanceTime ? value.latestAcceptanceTime.toFormat(DATE_TIME_FORMAT) : '',
					latestArrivalTime: value.latestArrivalTime ? value.latestArrivalTime.toFormat(DATE_TIME_FORMAT) : '',
					timeOfAvailability: value.timeOfAvailability ? value.timeOfAvailability.toFormat(DATE_TIME_FORMAT) : '',
				},
				unitsPreference,
				involvedParties: [],
			} as BookingOptionRequestDetailObj;

			if (this.bookingOptionRequestId) {
				param.id = this.bookingOptionRequestId;
			}
		}

		return param;
	}

	/**
	 * 手动标记所有 date-time-picker 组件为 touched 状态
	 */
	markDateTimePickersAsTouched(): void {
		this.dateTimePickerComponents?.forEach((picker) => picker.markAsTouched());
	}
}
