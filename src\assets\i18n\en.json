{"validators": {"required": "{{field}} is required", "email": "Please enter a valid email address", "number": "Please enter number", "pattern": "Please enter a valid {{requiredPattern}} value in {{field}}", "minlength": "Please enter at least {{minLength}} characters", "maxlength": "Please enter no more than {{maxLength}} characters", "min": "Please enter a value greater than or equal to {{min}}", "max": "Please enter a value less than or equal to {{max}}", "minDate": "Please enter a value greater than or equal to {{minDate}}", "maxOneDecimal": "{{field}} is positive number with maximum 1 decimal", "maxTwoDecimal": "{{field}} is positive number with maximum 2 decimal", "maxDecimal1": "positive number with maximum 1 decimal", "maxDecimal2": "positive number with maximum 2 decimal", "maxOneDecimalRound5": "{{field}} is positive number with maximum 1 decimal and round up to 0.5", "positiveNumber": "{{field}} is positive number"}, "common": {"mainHeader.app.title": "ONE Record", "mainHeader.app.subtitle": "Living Lab", "mainHeader.mainNav.shipment": "SLI", "mainHeader.mainNav.sli": "Piece", "mainHeader.mainNav.sli.piece": "Piece", "mainHeader.mainNav.hawb": "HAWB", "mainHeader.mainNav.mawb": "MAWB", "mainHeader.mainNav.changeRequests": "Change Requests", "mainHeader.mainNav.mawb.fhl": "House Manifest", "mainHeader.mainNav.upload": "Upload", "mainHeader.mainNav.payment": "Payment", "mainHeader.mainNav.fileBrowser": "File Browser", "mainHeader.mainNav.checkin": "Check-in", "mainHeader.mainNav.mf": "MF", "mainHeader.mainNav.users": "User", "mainHeader.mainNav.subscription": "Subscription", "mainHeader.mainNav.distribution": "Distribution", "mainHeader.mainNav.distribution.quote": "Quote", "mainHeader.mainNav.distribution.booking": "Booking", "mainHeader.language.english": "EN", "mainHeader.language.chinese": "中文", "mainHeader.settings": "Settings", "mainHeader.account": "Account", "mainHeader.userid": "User ID", "mainHeader.currentRole": "Current Role", "mainHeader.resources": "Resources", "mainHeader.logout": "Logout", "mainHeader.mainNav.partner": "Partner", "mainHeader.mainNav.partner.permission": "Permission", "mainHeader.mainNav.partner.subscription": "Subscription", "mainHeader.mainNav.system": "System", "mainHeader.userlogin.success": "<PERSON><PERSON> successfully", "mainHeader.userlogin.fail": "<PERSON><PERSON> failed", "mainFooter.title": "Our mission is to represent,", "mainFooter.subtitle": "lead and serve the airline industry", "mainFooter.support": "Support", "mainFooter.services": "Services", "mainFooter.store": "IATA store", "mainFooter.privacy": "Privacy", "mainFooter.legal": "Legal", "breadcrumb.home": "Home", "dialog.next": "Next", "dialog.cancel": "Cancel", "dialog.later": "Later", "dialog.ok": "OK", "dialog.revoke": "Revoke", "dialog.approve": "Approve", "dialog.reject": "Reject", "dialog.alert.title": "<PERSON><PERSON>", "dialog.alert.content.piece": "No permission granted on Pieces", "dialog.alert.content.403": "403 error", "dialog.request.delegation": "Request Access Delegation", "dialog.delegation.request.title": "Delegation Access Request", "dialog.delegation.request.description": "Description", "dialog.delegation.request.for": "Is Requested For", "dialog.delegation.request.by": "Is Requested By", "dialog.delegation.request.at": "Is Requested At", "dialog.delegation.request.status": "Request Status", "dialog.delegation.request.permissions": "Permissions", "dialog.delegation.request.logisticsObject": "Logistics Object", "dialog.delegation.request.create": "Raise Delegation Request", "dialog.delegation.request.send": "Send", "dialog.delegation.request.approve": "Approve", "dialog.delegation.request.reject": "Reject", "delegation.permission.GET_LOGISTICS_EVENT": "GET_LOGISTICS_EVENT", "delegation.permission.GET_LOGISTICS_OBJECT": "GET_LOGISTICS_OBJECT", "delegation.permission.POST_LOGISTICS_EVENT": "POST_LOGISTICS_EVENT", "delegation.permission.PATCH_LOGISTICS_OBJECT": "PATCH_LOGISTICS_OBJECT", "delegation.request.tab": "Access Delegation Requests", "dialog.cancel.content": "Are you sure to cancel?", "dialog.delete.content": "Are you sure to delete?", "dialog.unConsolidate.conent": "Are you sure to break down?", "selectOrg.title": "Select One from Below", "confirm.title": "Confirm", "dialog.form.validate": "Please fill out all the required and valid data.", "dialog.orglist.fail": "Get organization list failed", "dialog.orginfo.fail": "Get organization info failed", "change.request": {"object.type": "Logistic Object Type", "object.url": "Change Request URI", "version": "Version", "date": "Date", "changed.by": "Changed by", "status": "Status", "title": "Version History", "request.number": "MAWB Number", "dialog.title": "Change Request", "request.by": "Request By", "description": "Describe your change to Data Holder", "btn.revoke": "Revoke", "btn.reject": "Reject", "btn.approve": "Approve", "detail.table.obj": "Logistic Object", "detail.table.property": "Property", "detail.table.oldValue": "Initial Value", "detail.table.newValue": "New Value", "no.permission": "No permission to retrieve version info"}, "table.action": "Action", "copy.success": "URI is copied", "copy.failed": "URI copy failed", "copy.no.text": "No text to be copied", "retrieve.btn": "Retrieve", "enter.uri.btn": "Enter URI to Retrieve", "enter.uri.title": "Enter URI to Retrieve a ", "no.access.error": "Not authorized to perform action error then it can follow the Request Access Delegation flow to request access", "skip.btn": "<PERSON><PERSON>", "lo.access.error": "You don’t have permission for this logistics object"}, "sli": {"mgmt.title": "Piece Management", "mgmt.shipment": "SLI", "mgmt.goodsDescription": "Goods Description", "mgmt.sliCode": "SLI Code", "mgmt.shipper": "Shipper Company Name", "mgmt.consignee": "Consignee Company Name", "mgmt.departureLocation": "Origin", "mgmt.arrivalLocation": "Destination", "mgmt.hawbNumber": "Associated HAWB", "mgmt.createDate": "Created Date Range", "mgmt.search": "Search", "mgmt.reset": "Reset", "mgmt.create": "Create Pieces", "mgmt.createHawb": "Create", "mgmt.edit": "Edit Pieces", "mgmt.cancel": "Cancel", "mgmt.save": "Save", "mgmt.add.piece": "Piece", "mgmt.delete.piece": "Delete", "mgmt.list": "Piece", "mgmt.list.total.quantity": "Total Piece Quantity", "mgmt.list.total.slac": "Total SLAC", "mgmt.company": {"companyName": "Company Name", "contactName": "Contact Name", "country": "Country", "province": "Province", "city": "City", "textualPostCode": "Postal Code", "address": "Address", "phoneNumber": "Phone Number", "emailAddress": "Email Address", "shipper": "Shipper", "consignee": "Consignee", "alsoNotify": "Also Notify", "companyName.required": "Company Name is required", "contactName.required": "Contact Name is required", "country.required": "Country is required", "province.required": "Province is required", "city.required": "City is required", "address.required": "Address is required", "phoneNumber.required": "Phone Number is required", "pattern": {"number": "Please enter number", "email": "Please enter a valid email address"}}, "mgmt.routing": {"departureLocation": "Airport of Departure", "arrivalLocation": "Airport of Destination", "departureLocation.required": "Airport of Departure is required", "arrivalLocation.required": "Airport of Destination is required", "shippingInfo": "Accounting Information"}, "mgmt.pieceList": {"goodsDescription": "Description of Goods", "totalGrossWeight": "Total Gross Weight", "totalDimensions": "Dimensions", "dimLength": "Length", "dimWidth": "<PERSON><PERSON><PERSON>", "dimHeight": "Height", "goodsDescription.required": "Description of Goods is required", "totalGrossWeight.required": "Total Gross Weight is required", "dimLength.required": "Length is required", "dimWidth.required": "Width is required", "dimHeight.required": "Height is required", "declaredValueForCustoms": "Declared Value for Customs", "declaredValueForCarriage": "Declared Value for Carriage", "insuredAmount": "Insurance Amount Requested", "textualHandlingInstructions": "Handling Information Remarks", "weightValuationIndicator": "Weight Valuation Indicator", "incoterms": "Incoterms", "pattern": {"positiveNumber": "positive number", "decimalNumber1": "positive number with maximum 1 decimal", "decimalNumber2": "positive number with maximum 2 decimals", "decimalNumber2NIL": "positive number with maximum 2 decimals or NIL", "decimalNumber2NVD": "positive number with maximum 2 decimals or NVD", "decimalNumber2NCV": "positive number with maximum 2 decimals or NCV"}, "create.step1": "Step 1: Create Pieces"}, "piece": {"table.column.productDescription": "Product Description", "table.column.packagingType": "Packaging Type", "table.column.grossWeight": "Gross Weight(KG)", "table.column.dimensions": "Dimensions", "table.column.pieceQuantity": "Piece Quantity", "table.column.slac": "SLAC", "table.column.actions": "Actions", "table.column.latestStatus": "Latest Status", "addDialog.title": "Choose Piece Type", "addDialog.subtitle": "Please select piece type of you want to add", "addDialog.general": "General <PERSON>", "addDialog.dg": "Dangerous Goods", "addDialog.la": "Live Animals", "addDialog.pieceType.required": "Piece type is required", "grossWeight.required": "Gross Weight is required", "packagingType.required": "Packaging Type is required", "productDescription.required": "Product Description is required", "add": "Add Piece", "edit": "Edit Piece", "add.pieceIn": "Add", "title": "Piece", "productDescription": "Product Description", "grossWeight": "Gross Weight", "dimensions": "Dimensions", "upid": "UPID", "packages": "Packages", "packagingType": "Type of Package", "packagedIdentifier": "Packaged Identifier", "hsCommodityDescription": "HS Commodity Description", "nvdForCustoms": "Whether have Declared Value for Customs", "nvdForCarriage": "Whether have Declared Value for Carriage", "shippingMarks": "Shipping Marks", "textualHandlingInstructions": "Handling Instructions", "pieceQuantity": "Piece Quantity", "pieceQuantity.required": "Piece Quantity is required", "done": "Done", "item.title": "Do you want to create Contained Pieces in this Piece?", "item.add": "Add Item", "contained.yes": "Yes", "contained.no": "No", "contained.title": "Contained Pieces", "item.description": "Item Description", "item.weight": "Weight", "item.quantity": "Quantity", "item.total": "Total Items", "slac.total": "SLAC", "create.success": "Create piece successfully", "create.fail": "Create piece failed", "update.success": "Update piece successfully", "update.fail": "Update piece failed", "detail.fail": "Get piece detail failed", "list.fail": "Get piece list failed", "no.sli.fail": "Please create SLI first", "consolidate.title": "Consolidate Pieces", "consolidate.btn": "Consolidate"}, "dgPiece": {"title": "DG Piece", "formItem": {"productDescription": "Product Description", "typeOfPackage": "Type of Package", "packagedIdentifier": "Packaged Identifier", "whetherHaveDeclaredValueForCustoms": "Whether have Declared Value for Customs", "whetherHaveDeclaredValueForCarriage": "Whether have Declared Value for Carriage", "specialProvisionId": "Special Provision ID", "explosiveCompatibilityGroupCode": "Explosive Compatibility Group Code", "packagingDangerLevelCode": "Packaging Danger Level Code", "technicalName": "Technical Name", "unNumber": "UN Number", "shippersDeclaration": "<PERSON><PERSON>’s Declaration", "handlingInformation": "Handling Information", "allPackedInOne": "All Packed in One", "qValueNumeric": "Q Value Numeric", "upid": "UPID", "shippingMarks": "Shipping Marks", "grossWeight": "Gross Weight", "dimensions": "Dimensions", "hsCommodityDescription": "HS Commodity Description", "properShippingName": "Proper Shipping Name", "textualHandlingInstructions": "Textual Handling Instructions", "hazardClassificationId": "Hazard Classification ID", "additionalHazardClassificationId": "Additional Hazard Classification ID", "packingInstructionNumber": "Packing Instruction Number", "complianceDeclaration": "Compliance Declaration", "exclusiveUseIndicator": "Exclusive Use Indicator", "authorizationInformation": "Authorization Information", "aircraftLimitationInformation": "Aircraft Limitation Information", "item.productDescription": "Product Description", "item.quantity": "Quantity", "item.weight": "Weight", "item.title": "<PERSON><PERSON>", "item.add": "Add"}}, "liveAnimalPiece": {"title": "Live Animal Piece", "formItem": {"productDescription": "Product Description", "typeOfPackage": "Type of Package", "packagedIdentifier": "Packaged Identifier", "speciesCommonName": "Species Common Name", "speciesScientificName": "Species Scientific Name", "specimenDescription": "Specimen Description", "animalQuantity": "Animal Quantity", "shippingMarks": "Shipping Marks", "upid": "UPID", "grossWeight": "Gross Weight", "dimensions": "Dimensions", "whetherHaveDeclaredValueForCustoms": "Whether have Declared Value for Customs", "whetherHaveDeclaredValueForCarriage": "Whether have Declared Value for Carriage", "textualHandlingInstructions": "Textual Handling Instructions"}}, "table.column.sliCode": "SLI Code", "table.column.shipper": "Shipper", "table.column.consignee": "Consignee", "table.column.goodsDescription": "Goods Description", "table.column.departureLocation": "Origin", "table.column.arrivalLocation": "Destination", "table.column.slac": "Piece Quantity", "table.column.createDate": "Created Date", "table.column.receivedFrom": "Received From", "table.column.hawbNumber": "Associated HAWB", "table.column.share": "Share", "dialog.create.success": "Create SLI successfully", "dialog.create.fail": "Create SLI failed", "dialog.update.success": "Update SLI successfully", "dialog.update.fail": "Update SLI failed", "dialog.detail.fail": "Get SLI detail failed", "dialog.list.fail": "Get SLI list failed", "dialog.shipper.fail": "Get shipper info failed", "share.title": "Share SLI", "create.pieces.confirm": "Please finalize SLI creation later to associate these pieces"}, "shared": {"table.column.name": "Company Name", "table.column.orgType": "Company Type", "button.name": "Share"}, "hawb": {"mgmt.title": "House Air Waybill", "mgmt.hawbNumber": "HAWB Number", "mgmt.create": "Create HAWB from SLI", "mgmt.create.fromSli": "Create House Air Waybill from SLI", "mgmt.create.fromSliDetail": "Create House Air Waybill", "mgmt.edit": "Edit HAWB", "mgmt.fhl.total": "Total HAWB", "mgmt.fhl.total.slac": "Total SLAC", "mgmt.fhl.total.piece": "Total Piece", "mgmt.piecelist": "HAWB Piece List", "mgmt.total.piece": "Total Piece Quantity", "mgmt.associated.sli": "Associated SLI", "mgmt.latest.status": "Latest Status", "table.column.hawbNumber": "HAWB Number", "table.column.shipper": "Shipper", "table.column.consignee": "Consignee", "table.column.goodsDescription": "Goods Description", "table.column.origin": "Origin", "table.column.destination": "Destination", "table.column.pieceQuantity": "Piece Quantity", "table.column.createDate": "Created Date", "table.column.latestStatus": "Latest Status", "table.column.eventDate": "Event Date", "table.column.mawbNumber": "Associated MAWB", "table.column.share": "Share", "table.column.copy": "Copy", "table.column.totalGrossWeight": "Weight", "table.column.pieceNumber": "Number of Pieces", "table.column.slac": "SLAC", "table.column.textualHandlingInformation": "Special Handing Code", "table.column.countryCode": "Custom Information ISO Country Code", "table.column.contentCode": "Customs, Security and Regulatory Control Information Identifier", "table.column.otherCustomsInformation": "Supplementary Customs, Security and Regulatory Control Information", "dialog.list.fail": "Get HAWB list failed", "createHawb.success": "Create HAWB Success", "preview.awb": "Preview AWB", "updateHawb.success": "HAWB is successfully updated", "updateHawb.error": "HAWB update fails due to server unavailability, please try again", "share.title": "Share HAWB", "carrierAgent": {"title": "Carrier's Agent", "company": "Company", "agentIataCode": "Agent's IATA Code", "country": "Country", "province": "Province", "cityName": "City Name", "textualPostCode": "Postal Code", "address": "Address", "phoneNumber": "Phone Number", "email": "Email Address", "formInvalid": "Carrier's Agent Form Invalid"}, "issuedBy": {"title": "Issued By", "content": "To be populated when MAWB is created"}, "formItem": {"hawbPrefix": "HAWB Prefix", "hawbNumber": "HAWB Number", "accountingInformation": "Accounting Information", "handingInformation": "Handing Information", "noOfPiecesRcp": "No. of Pieces RCP", "grossWeight": "Gross Weight", "volume": "Volume", "rateClass": "Rate Class", "chargeableWeight": "Chargeable Weight", "rateCharge": "Rate/Charge", "total": "Total", "natureAndQuantityOfGoods": "Nature and Quantity of Goods (including Dimensions or Volume)", "date": "Date", "atPlace": "At(Place)", "signatureOfShipperOrHisAgent": "Signature of <PERSON><PERSON> or his Agent", "signatureOfCarrierOrItsAgent": "Signature of Carrier or its Agent"}, "airportInfo": {"departureAndRequestedRouting": "Airport of Departure (Addr, of First Carrier) and Requested Routing", "departureAndRequestedRouting.required": "Airport of Departure and Requested Routing is required", "airportOfDestination": "Airport Of Destination", "amountOfInsurance": "Amount Of Insurance", "flight": "Flight", "to": "To", "toBy2ndCarrier": "To (by 2nd_carrier)", "toBy3rdCarrier": "To (by 3rd carrier)", "date": "Date", "byFirstCarrier": "By First Carrier", "by2ndCarrier": "By 2nd carrier", "by3rdCarrier": "By 3rd carrier", "wtOrVal": "WT/VAL", "other": "Other", "declaredValueForCarriage": "Declared Value For Carriage", "declaredValueForCustoms": "Declared Value For Customs"}, "prepaidAndCollect": {"prepaidTitle": "Prepaid", "collectTitle": "Collect", "weightCharge": "Weight Charge", "valuationCharge": "Valuation Charge", "tax": "Tax", "totalOtherChargesDueAgent": "Total Other Charges Due Agent", "totalOtherChargesDueCarrier": "Total Other Charges Due Carrier", "totalPrepaid": "Total Prepaid", "totalCollect": "Total Collect"}, "otherCharges": {"title": "Other Charges", "chargePaymentType": "Charge Payment Type", "entitlement": "Entitlement", "otherChargeCode": "Other Charge Code", "otherChargeAmount": "Other Charge Amount", "addButton": "Other Charges", "formInvalid": "Carrier's Agent Form Invalid"}}, "mawb": {"mgmt.title": "Master Air Waybill", "mgmt.mawbNumber": "MAWB Number", "mgmt.create": "Create MAWB from HAWBs", "mgmt.create.fromHawb": "Create MAWB from my HAWBs", "mgmt.create.fromHawbDetail": "Master Air Waybill", "mgmt.create.fromSelectedHawb": "Select to create MAWB", "mgmt.edit": "Edit MAWB", "mgmt.export": "Export PDF", "mgmt.close": "Close", "mgmt.exporting": "Exporting...", "exportpdf.success": "Export PDF successfully", "exportpdf.failed": "Export PDF failed", "exportpdf.generating": "Generating PDF...", "mgmt.airlineCode": "Airline Code", "mgmt.latestStatus": "Latest Status", "mgmt.btn.post.request": "Post Change Request", "mgmt.accountingNoteText.shipper": "<PERSON><PERSON>'s Account Number", "mgmt.accountingNoteText.consignee": "Consignee's Account Number", "mgmt.shipment.shipper": "New Shipper", "mgmt.shipment.consignee": "New Consignee", "table.column.share": "Share", "table.column.copy": "Copy", "table.column.mawbNumber": "MAWB Number", "table.column.airlineCode": "Airline Code", "table.column.goodsDescription": "Goods Description", "table.column.origin": "Origin", "table.column.destination": "Destination", "table.column.latestStatus": "Latest Event", "table.column.eventDate": "Event Date", "table.column.createDate": "Created Date", "share.title": "Share MAWB", "createMawb.success": "Create MAWB Success", "preview.awb": "Preview AWB", "updateMawb.success": "MAWB is successfully updated", "post.success": "Post Change Request has been successfully sent", "updateMawb.error": "MAWB update fails due to server unavailability, please try again", "dialog.weight.validate": "selected HAWBs have different WT/VAL value", "dialog.other.validate": "selected HAWBs have different Other charge type value", "carrierAgent": {"title": "Carrier's Agent", "company": "Company", "agentIataCode": "Agent's IATA Code", "accountingNoteText": "Account No.", "country": "Country", "province": "Province", "cityName": "City Name", "textualPostCode": "Postal Code", "address": "Address", "phoneNumber": "Phone Number", "email": "Email Address", "formInvalid": "Carrier's Agent Form Invalid"}, "issuedBy": {"title": "Issued By", "content": "To be populated when MAWB is created"}, "formItem": {"mawbPrefix": "MAWB Prefix", "mawbNumber": "MAWB Number", "mawbNumber.checkLength": "MAWB Number must be 8 digits", "mawbNumber.checkDigit": "MAWB Number's 8th digit should be the mod7 of the first 7 digits, which is ", "accountingInformation": "Accounting Information", "handingInformation": "Handing Information", "noOfPiecesRcp": "No. of Pieces RCP", "grossWeight": "Gross Weight", "volume": "Volume", "serviceCode": "Service Code", "rateClass": "Rate Class", "chargeableWeight": "Chargeable Weight", "rateCharge": "Rate/Charge", "total": "Total", "natureAndQuantityOfGoods": "Nature and Quantity of Goods (including Dimensions or Volume)", "destinationCurrencyRate": "Currency Conversion Rate", "destinationCollectCharges": "Collect Charges in Destination Currency", "totalCollectCharges": "Total Collect Charges", "destinationCharges": "Charges at Destination", "shippingInfo": "Optional Shipping Information", "shippingRefNo": "Reference Number", "date": "Date", "atPlace": "At(Place)", "signatureOfShipperOrHisAgent": "Signature of <PERSON><PERSON> or his Agent", "signatureOfCarrierOrItsAgent": "Signature of Carrier or its Agent"}, "airportInfo": {"departureAndRequestedRouting": "Airport of Departure and Requested Routing", "airportOfDestination": "Airport Of Destination", "amountOfInsurance": "Amount Of Insurance", "chargesCode": "Charges Code", "flight": "Flight", "to": "To", "toBy2ndCarrier": "To (by 2nd_carrier)", "toBy3rdCarrier": "To (by 3rd carrier)", "date": "Date", "byFirstCarrier": "By First Carrier", "by2ndCarrier": "By 2nd carrier", "by3rdCarrier": "By 3rd carrier", "wtOrVal": "WT/VAL", "other": "Other", "declaredValueForCarriage": "Declared Value For Carriage", "declaredValueForCustoms": "Declared Value For Customs"}, "prepaidAndCollect": {"prepaidTitle": "Prepaid", "collectTitle": "Collect", "weightCharge": "Weight Charge", "valuationCharge": "Valuation Charge", "tax": "Tax", "totalOtherChargesDueAgent": "Total Other Charges Due Agent", "totalOtherChargesDueCarrier": "Total Other Charges Due Carrier", "totalPrepaid": "Total Prepaid", "totalCollect": "Total Collect"}, "otherCharges": {"title": "Other Charges", "chargePaymentType": "Charge Payment Type", "entitlement": "Entitlement", "otherChargeCode": "Other Charge Code", "otherChargeAmount": "Other Charge Amount", "addButton": "Other Charges", "formInvalid": "Carrier's Agent Form Invalid"}, "event": {"update.title": "Event Update", "milestone.date": "Specify if the milestone took place the other day", "planned.milestone": "This is a planned milestone", "partial.milestone": "This is a partially reached milestone", "update.btn": "Update", "bulk.update.btn": "Bulk Update", "update.log.btn": "Update Log", "update.log.title": "Log Items", "update.log.operationStatus": "Operation Status", "update.log.loId": "Logistics Objects Id", "update.log.type": "Type", "update.log.newValue": "Milestone", "update.log.errorMsg": "Error <PERSON>", "update.log.createDate": "Create Date", "update.log.userName": "Update By", "update.log.orgName": "Org Name", "time.type.error": "empty event time type", "update.time.error": "empty update time", "empty.error": "Please select the event", "choose.mawb": "Please select MAWB/HAWB/PIECE", "tracking.title": "Event Tracking", "status.history": "Status History", "history.table.event": "Event", "history.table.time": "Update Time", "history.table.user": "Update By", "history.table.event.time.type": "Event Time Type", "history.table.partial.event": "Partial Event", "no.permission": "No permission to retrieve Logistic Events", "back.btn": "Back"}}, "partner": {"mgmt.head": "Partner Access", "mgmt.title": "Please note following rules have been pre-configured as such:", "mgmt.title1": "1. Airlines can only access MAWBs if MAWB Prefix = Airline Settlement Code with full permissions granted", "mgmt.title2": "2. Shippers can only access HAWBs created from his company's SLIs with full permissions granted.", "mgmt.mawbNumber": "MAWB Number", "mgmt.addPartner": "Add Partner", "mgmt.edit": "Edit", "mgmt.save": "Save", "mgmt.latestStatus": "Latest Status", "table.column.businessData": "Business Data", "table.column.partner": "Partner", "table.column.GET_LOGISTICS_OBJECT": "GET_LOGISTICS_OBJECT", "table.column.PATCH_LOGISTICS_OBJECT": "PATCH_LOGISTICS_OBJECT", "table.column.POST_LOGISTICS_EVENT": "POST_LOGISTICS_EVENT", "table.column.GET_LOGISTICS_EVENT": "GET_LOGISTICS_EVENT", "tab.configuration": "Configuration", "tab.request": "Request"}, "upload": {"browser.dragAndDrop": "Drag & Drop file(s) here", "browser.or": "or", "browser.browse": "Browse for file(s)", "progress.lastModified": "Last modified at:", "progress.fileSize": "File size:"}, "filesManagement": {"category": "File category", "table.column.name": "Name", "table.column.size": "Size", "table.column.createdAt": "Created at", "table.column.actions": "Actions", "table.action.view": "View", "table.action.download": "Download", "table.action.delete": "Delete", "noFilesFound": "Sorry, we didn't find any files. You might need to upload them first!", "selectedFile.header": "Selected file view"}, "ifgPayment": {"paymentInitFailed": "Payment initiation failed!", "renderFailed": "Payment screen rendering failed!"}, "checkin": {"passenger": {"title": "Passenger details", "name": "Full Name", "gender": "Gender", "nationality": "Nationality", "nationality.placeholder": "Select Nationality", "birthDate": "Birth Date"}, "flight": {"title": "Flight details", "carrier": "Operating Carrier", "carrier.placeholder": "Select Carrier", "departureAirport": "Departure Airport", "departureAirport.placeholder": "Select Airport", "departureDate": "Departure Date", "arrivalAirport": "Arrival Airport", "arrivalAirport.placeholder": "Select Airport", "arrivalDate": "Arrival Date", "flightNumber": "Flight Number", "addLeg": "Add Leg", "deleteLeg": "Delete Leg", "checkin": "Check-in"}, "boardingPass": {"title": "Here is your boarding pass!", "subTitle": "Send boarding pass to your e-mail address:", "email": "Email", "send": "Send"}}, "users": {"mgmt.list": "User Management", "mgmt.keyword": "Please input keyword to search", "mgmt.search": "Search", "table.column.userName": "User Name", "table.column.email": "User Email", "table.column.firstName": "First Name", "table.column.lastName": "Last Name", "table.column.orgName": "Company/Organization", "table.column.primaryOrgName": "Primary Resident Company", "table.column.userType": "User Type", "table.column.roles": "Roles", "table.column.status": "Status", "table.column.lastAccessed": "Last Accessed At", "table.column.actions": "Actions", "table.action.updateUser": "Update User", "buttons.create": "Add User", "noDataFound": "We didn't find any existing user!", "mgmt.create.title": "Create User", "mgmt.create.firstName": "First Name", "mgmt.create.firstName.required": "First Name is required", "mgmt.create.lastName": "Last Name", "mgmt.create.lastName.required": "Last Name is required", "mgmt.create.email": "Email", "mgmt.create.email.required": "Email is required", "mgmt.create.orgName": "Company/Organization", "mgmt.create.orgName.required": "Company/Organization is required", "mgmt.create.primaryOrgName": "Primary Resident Company", "mgmt.create.userType": "User Type", "mgmt.create.userType.required": "User Type are required", "mgmt.create.primaryOrgName.required": "Primary Resident Company is required", "mgmt.create.secondary": "Secondary Resident Companies", "mgmt.create.secondary.orgName": "Resident Company", "mgmt.create.secondary.add": "Add", "mgmt.create.email.iataEmail": "Email should be @iata.org or @external.iata.org", "create.email.duplicatedUser": "User with such email already exists", "create.roles": "Roles", "create.roles.required": "Roles are required", "create.status": "Status", "create.status.required": "Status is required", "create.cancel": "Cancel", "create.submit": "Save", "update.title": "Update User", "update.submit": "Save"}, "pagination": {"itemsPerPage": "Records per page", "nextPage": "Next page", "previousPage": "Previous page", "firstPage": "First page", "lastPage": "Last page", "rangeEmpty": "0 of 0", "rangeLabel": "{{start}} - {{end}} of {{length}}"}, "subscription": {"title": "Subscription", "booking.requset.text": "Airlines can only subscribe to Booking Requests raised for its own Booking Options", "request.table.subscriber": "Subscriber", "request.table.publisher": "Publisher", "request.table.topicType": "Topic Type", "request.table.topic": "Topic", "request.table.requestBy": "Is Requested By", "request.table.requestAt": "Is Requested At", "request.table.status": "Request Status", "request.detail": "Request Details", "detail.permissions": "Permissions", "tab.configuration": "Configuration", "tab.request": "Request", "config.table.eventType": "Subscription Event Type", "config.table.expiersAt": "Expires At", "btn.new": "New Subscription", "btn.invite": "Invite to Subscribe", "create.title": "Create Subscription", "edit.title": "Edit Subscription", "form.event": "Subscription Event Type", "btn.invite.confirm": "Invite", "topic.placeholder": "input URI in here..."}, "notifications": {"title": "Notifications", "view.more": "Read all Notifications", "toggle.read": "Only Unread", "btn.mark": "<PERSON> as <PERSON>", "share.content": " has shared ", "updated": " has been updated ", "created": " has been created ", "event.content": " has been added an event"}, "system": {"title": "System Management", "rule": {"title": "Rule", "table.holder": "Holder", "table.request.type": "Request Type", "table.requester": "Requester", "table.action": "Action", "info": "All Change Requests on any Logistic Objects as part of a MAWB are auto-rejected if the MAWB (Shipment:Master) has RCS event posted", "approve": "Auto Approve", "reject": "Auto Reject", "create.title": "Create Rule", "edit.title": "Edit Rule", "requester.error1": "holders are more than one, requesters must be empty", "requester.error2": "requester can not include holders", "booking.info": "Booking Options patched to my Booking Option Request by any Airlines are auto-approved so that Forwarder can always see received Booking Options."}, "server": {"title": "Server", "matpanel.title.server": "Server", "matpanel.label.uri": "URI", "matpanel.label.endpoint": "Endpoint", "matpanel.label.apiVersion": "API Version", "matpanel.label.contentType": "Content Type", "matpanel.label.encoding": "Encoding", "matpanel.label.language": "Language", "matpanel.label.ontology": "Ontology", "matpanel.label.ontologyVersion": "Ontology Version", "matpanel.title.organization": "Organization", "matpanel.label.id": "ID", "matpanel.label.residentsType": "Residents Type", "matpanel.label.companyName": "Company Name", "matpanel.label.forwarderIATACode": "Forwarder IATA Code", "matpanel.label.airlineCode": "Airline Code", "matpanel.label.airlinePrefix": "Airline Prefix", "matpanel.label.country": "Country", "matpanel.label.province": "Province", "matpanel.label.city": "City", "matpanel.label.address": "Address", "matpanel.label.postCode": "Post Code", "matpanel.title.keycloak": "Keycloak", "warn": "Usually you should NOT change Keycloak settings. Do you confirm to change?", "matpanel.label.graphDbUrl": "GraphDb Url", "matpanel.label.neOneUrl": "NeOne Url", "matpanel.label.keycloakUrl": "Keycloak Url", "matpanel.label.grantType": "GrantType", "matpanel.label.clientId": "Client Id", "matpanel.label.clientSecret": "Client Secret", "matpanel.label.logisticsAgentUri": "Logistics Agent <PERSON><PERSON>", "matpanel.title.contact": "Contact", "matpanel.label.contactRole": "Contact Role", "matpanel.label.jobTitle": "Job Title", "matpanel.label.contactName": "Contact Name", "matpanel.label.contactDetailType": "Contact Detail Type", "matpanel.label.textualValue": "Textual Value", "button.retrieveServerInfo": "Retrieve Server Info", "button.retrieveOrganizationInfo": "Retrieve Organization Info", "button.retrieveContactInfo": "Retrieve Contact Info", "button.save": "Save", "button.cancel": "Cancel", "button.addNewORLLResident": "Add New ORLL Resident", "button.addNewExternalServer": "Add New External Server", "button.deleteExternalServer": "Delete External Server", "tab.oRLLResident": "ORLL Resident", "tab.externalServers": "External Servers", "contact.new": "Add Contact", "retrive.org.btn": "Retrieve Organization Info", "retrive.contact.btn": "Retrieve Contact Info", "uri": "URI", "matpanel.title.permission": "Menu Permission"}}, "booking": {"option": {"table.product": "Requested Product", "table.departure": "Requested Departure", "table.arrival": "Requested Arrival", "table.status": "Status", "create.btn": "Request Quote for this MAWB", "create.quote.btn": "Raise Booking Option Request", "view.title": "Booking Option Request", "chargeable.weight": "Chargeable Weight", "commodity.code": "Commodity Code", "handling.code": "Special Handling Codes", "handling.instruction": "Handling Instructions", "subTitle.itinerary": "Itinerary", "departure.location": "Departure Location", "arrival.location": "Arrival Location", "max.segment": "Max Segments", "transport.id": "Preferred Transport ID", "earliest.acceptance.time": "Earliest Acceptance Time", "latest.acceptance.time": "Latest Acceptance Time", "latest.arrival.time": "Latest Arrival Time", "shipment.available.date": "Shipment Available Date", "currency": "<PERSON><PERSON><PERSON><PERSON>", "subTitle.preference": "Preference", "subTitle.booking.shipment": "Booking Shipment", "select.airline": "Select an Airline", "request.title": "Booking Option Request from ", "to.forwarder": "Send back to Forwarder", "request.booking": "Request Booking ", "from": " - IATA Code #", "form.departure": "Airport of Departure", "form.arrival": "Airport of Arrival", "form.airline.code": "Operating Carrier ( Airline Code)", "form.flight.number": "Flight Number", "form.departure.time": "Departure Date/Time", "form.arrival.time": "Arrival Date/Time", "form.charge.type": "Charge Type", "form.charge.rate.class": "Charge Rate Class", "form.rate": "Rate/Weight Charge", "form.payment.type": "Payment Type", "form.entitlement": "Entitlement", "form.product.code": "Product Code", "form.offer.valid.from": "Offer <PERSON><PERSON>", "form.offer.valid.to": "Offer <PERSON><PERSON>", "add.charge.btn": "Add Charge", "del.itinerary": "Remove Itinerary", "add.itinerary": "Add Itinerary", "send.back.btn": "Send back to Forwarder", "add.booking.option.btn": "Add Booking Option", "departure.check.warning": "Departure location should match previous arrival location", "price.title": "Price", "grand.total": "Grand Total =", "share.history": "Share History", "request.to": "Requested To", "request.date": "Requested Date", "created.succ": "Raise booking option request for mawb successfully", "created.failed": "Raise booking option request for mawb failed", "select": "Please select one booking option", "iata.code": "Code  "}, "mgmt.requestedBy": "Requested By", "mgmt.requestedProduct": "Requested Product", "mgmt.requestedFlight": "Requested Flight", "mgmt.requestedStatus": "Requested Status", "mgmt.departureLocation": "Departure Location", "mgmt.arrivalLocation": "Arrival Location", "mgmt.flightDate": "Flight Date", "mgmt.mawbNumber": "Associated MAWB", "mgmt.create": "Create Booking Request", "mgmt.save": "Save", "request.pieceGroupCount": "Piece Quantity", "request.totalGrossWeight": "Gross Weight", "request.chargeableWeight": "Chargeable Weight", "request.totalDimensions": "Dimensions", "request.expectedCommodity": "Commodity Code", "request.specialHandlingCodes": "Special Handling Codes", "request.textualHandlingInstructions": "Handling Instructions", "request.create.btn": "Request Booking for this MAWB", "request.totalPrice": "Price Grand Total", "dialog.title.option": "Booking Option Request", "dialog.title.request": "Booking Request", "dialog.button.request": "Request Booking", "dialog.button.confirm": "Confirm Booking", "dialog.button.create.master": "Create MAWB", "dialog.button.update.master": "Update MAWB", "dialog.button.mawb": "Create MAWB"}, "ecsd": {"title": "eCSD", "create.btn": "Create eCSD", "edit.btn": "Edit eCSD", "view.btn": "View eCSD", "table.status": "Security Status", "table.method": "Screening Method", "table.ground": "Grounds for Exemption", "table.from": "Received From", "table.issue.by": "Issued By", "table.issue.on": "Issued On", "hawb.table.number": "HAWB Number", "hawb.table.shipper": "Shipper", "hawb.table.consignee": "Consignee", "hawb.table.description": "Goods Descripiton", "hawb.table.origin": "Origin", "hawb.table.destination": "Destination", "hawb.table.weight": "Weight", "hawb.table.slac": "SLAC", "info.regulated": "Regulated Entity Category and Identifier (of the regulated party issuing the security status)", "info.reason": "Reasons for issuing the Security Status", "info.accepted": "Regulated Entity Category and Identifier (of any regulated party who has accepted the security status given to a consignment by another regulated party)", "form.category": "Regulated Entity Category", "form.identifier": "Regulated Entity Identifier", "form.status": "Security Status", "form.screen": "Whether exempted for Screening", "form.method": "Screening Method", "form.ground": "Grounds for Exemption", "form.from": "Received From", "form.issue.by": "Issued By/Employee ID", "form.issue.on": "Issued On", "form.information": "Additional Security Information", "table.piece.number": "Piece Number", "table.hawb.number": "HAWB Number", "table.mawb.number": "MAWB Number", "table.air.code": "Airline Code", "table.good.description": "Goods Description", "table.package.type": "Packaging Type", "table.gross.weight": "Gross Weight", "table.dimensions": "Dimensions", "yes": "Yes", "no": "No", "save.btn": "Save"}, "calculate": {"title": "Calculate Volume and Chargeable Weight", "column.productDescription": "Product Description", "column.dimensions": "Dimensions", "column.grossWeight": "Gross Weight", "column.slac": "slac", "label.pieceRCP": "No. of Piece:", "label.pieceGrossWeight": "Piece Gross Weight:", "label.totalSlac": "Total Slac:", "label.totalGrossWeight": "Total Gross Weight", "label.volume": "Volume", "label.chargeableWeight": "Chargeable Weight", "btn.save": "Save", "btn.cancel": "Cancel"}, "home": {"dashboard": "Dashboard", "dashboard.title": "Welcome to ONE Record Living Lab, a demo environment to explore IATA ONE Record capabilities.", "dashboard.links": "Useful Links", "dashboard.links.standard.title": "ONE Record Standard Introduction", "dashboard.links.standard": "Introduction to ONE Record, a standard for data sharing and creates a single record view of the shipment", "dashboard.links.api.title": "ONE Record API Specification", "dashboard.links.api": "This ONE Record API specification is part of the ONE Record standard. It defines a standard, programming language-agnostic interface for the interaction with the ONE Record Web API.", "dashboard.links.dataModel.title": "ONE Record Data Model Specification", "dashboard.links.dataModel": "This ONE Record data model specification is part of the ONE Record standard. It details core concepts of the data model described in the ONE Record cargo and ONE Record core code lists ontologies. It aims to provide explanations and examples for usage of the data model for implementers.", "dashboard.chart.weeklyTotal": "Weekly Total New User", "dashboard.chart.weeklyExternal": "Weekly External New User", "dashboard.chart.weeklyActive": "Weekly Active User", "dashboard.chart.weeklyTotalUser": "Weekly Total Registered User", "dashboard.chart.weeklyTotalExternalUser": "Weekly Total External User", "dashboard.chart.weeklyTotalActiveUser": "Weekly Total Active User", "dashboard.chart.userNumber": "User Number", "dashboard.chart.growthNumber": "Growing Number", "dashboard.chart.growthRate": "Growth Rate", "dashboard.activity.stream": "Activity Stream", "dashboard.activity.stream.clean": "Clean Up Activity Stream", "dashboard.activity.system.clean": "Clean Up System Data", "dashboard.activity.stream.clean.msg": "Are you sure to clean up all the Activity Stream logs? system data such as Shipment records(SLI/HAWB/MAWB) are not impacted.", "dashboard.activity.system.clean.msg": "Are you sure to clean up all the system data such as Shipment records(SLI/HAWB/MAWB), Change/Subscription/Access Delegation Requests, Subscriptions, Quote/Bookings and Notifications?", "dashboard.activity.clean": "Clean", "dashboard.activity.stream.download": "Download Records and Json"}}