import {
	ChangeDetectionStrategy,
	Component,
	OnInit,
	ViewChild,
	ElementRef,
	AfterViewInit,
	OnDestroy,
	ChangeDetectorRef,
} from '@angular/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BaseChartDirective, provideCharts } from 'ng2-charts';
import {
	ChartConfiguration,
	ChartOptions,
	ChartType,
	CategoryScale,
	LinearScale,
	PointElement,
	LineElement,
	LineController,
	Title,
	Tooltip,
	Legend,
	Filler,
} from 'chart.js';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { AsyncPipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { IataDateFormatPipe } from '@shared/utils/date-format.pipe';
import { HomeDashboardService } from '../../services/home-dashboard.service';
import { ActivityStream } from '../../models/home-dashboard.model';
import { downloadBinaryFile } from '@shared/utils/common.utils';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { DashboardChartType } from '../../models/chart-type.model';

@Component({
	selector: 'orll-home-dashboard',
	templateUrl: './home-dashboard.component.html',
	styleUrl: './home-dashboard.component.scss',
	imports: [
		MatDividerModule,
		MatIconModule,
		MatButtonModule,
		MatTooltipModule,
		TranslateModule,
		BaseChartDirective,
		AsyncPipe,
		IataDateFormatPipe,
	],
	providers: [
		provideCharts({
			registerables: [CategoryScale, LinearScale, PointElement, LineElement, LineController, Title, Tooltip, Legend, Filler],
		}),
	],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export default class HomeDashboardComponent extends RolesAwareComponent implements OnInit, AfterViewInit, OnDestroy {
	public lineChartType: ChartType = 'line';

	weeks: string[] = [];
	weeklyTotalData: number[] = [];
	weeklyExternalData: number[] = [];
	weeklyActiveData: number[] = [];

	totalUsers = 0;
	totalGrowthNumber = '';
	totalGrowthRate = '';

	totalExternalUsers = 0;
	externalGrowthNumber = '';
	externalGrowthRate = '';

	totalActiveUsers = 0;
	activeGrowthNumber = '';
	activeGrowthRate = '';

	fillGradientTotal: CanvasGradient | undefined;
	fillGradientExternal: CanvasGradient | undefined;
	fillGradientActive: CanvasGradient | undefined;

	activityStreamData: ActivityStream[] = [];

	@ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef<HTMLDivElement>;
	@ViewChild('scrollContent', { static: false }) scrollContent!: ElementRef<HTMLDivElement>;

	private scrollInterval: any;
	private readonly scrollSpeed = 1; // scroll speed (pixel/frame)
	private scrollDirection = 1; // 1: down, -1: up

	useSysClean = false;

	constructor(
		private readonly translateService: TranslateService,
		private readonly homeDashboardService: HomeDashboardService,
		private readonly cdr: ChangeDetectorRef,
		private readonly dialog: MatDialog
	) {
		super();
	}

	ngOnInit(): void {
		this.initChart();
	}

	private initChart(): void {
		this.getUserStatistics();
		this.getActivityStream();

		const canvas = document.createElement('canvas');
		const ctx = canvas.getContext('2d');

		this.fillGradientTotal = ctx!.createLinearGradient(0, 0, 0, 400);
		this.fillGradientTotal.addColorStop(0, 'rgba(40, 150, 50, 1)');
		this.fillGradientTotal.addColorStop(0.5, 'rgba(40, 150, 50, 0.7)');
		this.fillGradientTotal.addColorStop(1, 'rgba(40, 150, 50, 0.2)');

		this.fillGradientExternal = ctx!.createLinearGradient(0, 0, 0, 400);
		this.fillGradientExternal.addColorStop(0, 'rgba(250, 200, 50, 1)');
		this.fillGradientExternal.addColorStop(0.5, 'rgba(250, 200, 50, 0.7)');
		this.fillGradientExternal.addColorStop(1, 'rgba(250, 200, 50, 0.2)');

		this.fillGradientActive = ctx!.createLinearGradient(0, 0, 0, 400);
		this.fillGradientActive.addColorStop(0, 'rgba(240, 70, 50, 1)');
		this.fillGradientActive.addColorStop(0.5, 'rgba(240, 70, 50, 0.7)');
		this.fillGradientActive.addColorStop(1, 'rgba(240, 70, 50, 0.2)');
	}

	public getLineChartData(weeklyTotalData: number[], fillGradientTotal: CanvasGradient | undefined, themeColor: string) {
		const lineChartData: ChartConfiguration<'line'>['data'] = {
			labels: this.weeks,
			datasets: [
				{
					data: weeklyTotalData,
					label: this.translateService.instant('home.dashboard.chart.userNumber'),
					fill: true,
					borderWidth: 4,
					backgroundColor: fillGradientTotal,
					borderColor: themeColor,
					pointBackgroundColor: themeColor,
					pointBorderColor: '#fff',
					pointBorderWidth: 3,
					pointRadius: 7,
					pointHoverRadius: 10,
					pointHoverBorderColor: '#fff',
					pointHoverBorderWidth: 4,
					tension: 0.4,
				},
			],
		};

		return lineChartData;
	}

	public getLineChartOptions(text: string) {
		const lineChartOptions: ChartOptions<'line'> = {
			responsive: true,
			maintainAspectRatio: false,
			plugins: {
				title: {
					display: true,
					text: this.translateService.instant(text),
					color: '#333333',
					font: {
						size: 16,
						weight: 'bold',
					},
				},
				legend: {
					display: false,
				},
			},
			scales: {
				x: {
					display: false,
				},
				y: {
					display: false,
				},
			},
			elements: {
				line: {
					tension: 0.4,
				},
			},
		};
		return lineChartOptions;
	}

	downloadActivityFile(activity: ActivityStream): void {
		this.homeDashboardService.downloadFile(activity.url).subscribe((res) => {
			downloadBinaryFile(res, activity.fileName, false);
		});
	}

	getUserStatistics() {
		this.homeDashboardService.getUserStatistics().subscribe((res) => {
			const weeklyTotalData = res.find((d) => d.type === DashboardChartType.WEEKLY_TOTAL_NEW_USER);
			const weeklyExternalData = res.find((d) => d.type === DashboardChartType.WEEKLY_EXTERNAL_NEW_USER);
			const weeklyActiveData = res.find((d) => d.type === DashboardChartType.WEEKLY_ACTIVE_USER);

			this.weeks = weeklyTotalData?.trendData?.weeks ?? [];
			this.weeklyTotalData = weeklyTotalData?.trendData?.data ?? [];
			this.weeklyExternalData = weeklyExternalData?.trendData?.data ?? [];
			this.weeklyActiveData = weeklyActiveData?.trendData?.data ?? [];

			this.totalUsers = this.weeklyTotalData.reduce((a, b) => a + b, 0);
			this.totalExternalUsers = this.weeklyExternalData.reduce((a, b) => a + b, 0);
			this.totalActiveUsers = this.weeklyActiveData.reduce((a, b) => a + b, 0);

			this.totalGrowthNumber = weeklyTotalData?.growingNumber ?? '';
			this.totalGrowthRate = weeklyTotalData?.growthRate ?? '';

			this.externalGrowthNumber = weeklyExternalData?.growingNumber ?? '';
			this.externalGrowthRate = weeklyExternalData?.growthRate ?? '';

			this.activeGrowthNumber = weeklyActiveData?.growingNumber ?? '';
			this.activeGrowthRate = weeklyActiveData?.growthRate ?? '';

			this.cdr.markForCheck();
		});
	}

	getActivityStream() {
		this.homeDashboardService.getActivityStream().subscribe((res) => {
			this.activityStreamData = res.rows;
			this.cdr.markForCheck();
		});
	}

	cleanSystemData() {
		this.homeDashboardService.cleanSystemData().subscribe((res) => {
			if (res) {
				this.getActivityStream();
			}
		});
	}

	cleanActivityStream() {
		this.homeDashboardService.cleanActivityStream().subscribe((res) => {
			if (res) {
				this.getActivityStream();
			}
		});
	}

	openConfirmDialog(title: string, content: string): void {
		this.dialog
			.open(ConfirmDialogComponent, {
				width: '400px',
				data: {
					icon: 'cleaning_services',
					ok: this.translateService.instant('home.dashboard.activity.clean'),
					title: this.translateService.instant(title),
					content: this.translateService.instant(content),
				},
			})
			.afterClosed()
			.subscribe((result) => {
				if (result) {
					if (title.includes('system')) {
						this.cleanSystemData();
					} else {
						this.cleanActivityStream();
					}
				}
			});
	}

	ngAfterViewInit(): void {
		setTimeout(() => {
			this.startAutoScroll();
		}, 1000);
	}

	ngOnDestroy(): void {
		this.stopAutoScroll();
	}

	private startAutoScroll(): void {
		if (!this.scrollContainer || !this.scrollContent) {
			return;
		}

		this.scrollInterval = setInterval(() => {
			this.performScroll();
		}, 50);
	}

	private stopAutoScroll(): void {
		if (this.scrollInterval) {
			clearInterval(this.scrollInterval);
			this.scrollInterval = null;
		}
	}

	private performScroll(): void {
		if (!this.scrollContainer || !this.scrollContent) {
			return;
		}

		const container = this.scrollContainer.nativeElement;
		const content = this.scrollContent.nativeElement;

		const containerHeight = container.clientHeight;
		const contentHeight = content.scrollHeight;
		const currentScrollTop = container.scrollTop;

		// calculate new scroll position
		const newScrollTop = currentScrollTop + this.scrollSpeed * this.scrollDirection;

		// check if need to change direction
		if (newScrollTop <= 0) {
			this.scrollDirection = 1; // scroll down
			container.scrollTop = 0;
		} else if (newScrollTop >= contentHeight - containerHeight) {
			this.scrollDirection = -1; // scroll up
			container.scrollTop = contentHeight - containerHeight;
		} else {
			container.scrollTop = newScrollTop;
		}
	}
}
