<div class="orll-shipper-or-consignee-box">
	@if (title) {
		<div class="orll-shipper-or-consignee-box__header">
			<h2 class="mat-display-2 orll-shipper-or-consignee-box__title">
				{{ 'sli.mgmt.company.' + title | translate }}
			</h2>
			<mat-icon
				matSuffix
				class="autocomplete-arrow"
				(click)="$event.stopPropagation(); isOpen = !isOpen"
				(keydown.enter)="$event.stopPropagation(); isOpen = !isOpen"
				cdkOverlayOrigin
				#trigger="cdkOverlayOrigin"
				>keyboard_arrow_down</mat-icon
			>

			@if (shipmentParty?.id) {
				<mat-icon
					color="primary"
					[orllCopy]="shipmentParty?.id"
					(keydown.enter)="$event.stopPropagation()"
					class="orll-shipper-or-consignee-box__header__contact-button"
					>content_copy</mat-icon
				>
			}
		</div>

		<ng-template
			cdkConnectedOverlay
			[cdkConnectedOverlayOrigin]="trigger"
			[cdkConnectedOverlayOpen]="isOpen"
			(backdropClick)="isOpen = false"
			[cdkConnectedOverlayHasBackdrop]="true">
			<ul class="orll-shipper-or-consignee-box__secondary-nav__dropdown-list">
				<div
					class="new-party"
					(click)="$event.stopPropagation(); isOpen = false; shipperConsigneeForm.reset(); shipmentParty = null"
					(keydown.enter)="$event.stopPropagation(); isOpen = false; shipperConsigneeForm.reset(); shipmentParty = null">
					<mat-icon matPrefix>add</mat-icon>
					<div class="add-title">{{ 'mawb.mgmt.shipment.' + title | translate }}</div>
				</div>
				<mat-divider class="divider"></mat-divider>
				@for (hawb of getUniqueParties(hawbList, title); track hawb.hawbId) {
					<li class="orll-shipper-or-consignee-box__secondary-nav__dropdown-list__item">
						<a
							class="orll-shipper-or-consignee-box__secondary-nav__dropdown-list__item__link"
							(click)="$event.stopPropagation(); onSelectParty(hawb.hawbId, title)"
							(keydown.enter)="$event.stopPropagation(); onSelectParty(hawb.hawbId, title)">
							<span>{{ title === 'shipper' ? hawb.shipper : hawb.consignee }}</span>
						</a>
					</li>
				}
			</ul>
		</ng-template>
	}

	<form [formGroup]="shipperConsigneeForm">
		<div class="row">
			<mat-form-field appearance="outline" class="col-6" floatLabel="always">
				<mat-label>{{ 'sli.mgmt.company.companyName' | translate }}</mat-label>
				<input matInput formControlName="companyName" required />
				@if (shipperConsigneeForm.get('companyName')?.hasError('required')) {
					<mat-error>{{ 'sli.mgmt.company.companyName.required' | translate }}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-6" floatLabel="always">
				<mat-label>{{ 'mawb.mgmt.accountingNoteText.' + title | translate }}</mat-label>
				<input matInput formControlName="accountingNoteText" />
			</mat-form-field>
		</div>

		<div class="row">
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				<mat-label>{{ 'sli.mgmt.company.country' | translate }}</mat-label>
				<input type="text" matInput formControlName="countryCode" [matAutocomplete]="autoCountry" />
				<mat-autocomplete
					#autoCountry="matAutocomplete"
					[displayWith]="displayCountryName"
					(optionSelected)="countryValueChange($event)">
					@for (country of filteredCountries; track country.code) {
						<mat-option [value]="country.code">{{ country.name }}</mat-option>
					}
				</mat-autocomplete>
				@if (shipperConsigneeForm.get('countryCode')?.hasError('required')) {
					<mat-error>{{ 'sli.mgmt.company.country.required' | translate }}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				<mat-label>{{ 'sli.mgmt.company.province' | translate }}</mat-label>
				<input type="text" matInput formControlName="regionCode" [matAutocomplete]="autoRegion" />
				<mat-autocomplete
					#autoRegion="matAutocomplete"
					[displayWith]="displayProvinceName"
					(optionSelected)="regionValueChange($event)">
					@for (province of filteredProvinces; track province.code) {
						<mat-option [value]="province.code">{{ province.name }}</mat-option>
					}
				</mat-autocomplete>
				@if (shipperConsigneeForm.get('regionCode')?.hasError('required')) {
					<mat-error>{{ 'sli.mgmt.company.province.required' | translate }}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				<mat-label>{{ 'sli.mgmt.company.city' | translate }}</mat-label>
				<input type="text" matInput formControlName="cityCode" [matAutocomplete]="autoCity" />
				<mat-autocomplete #autoCity="matAutocomplete" [displayWith]="displayCityName">
					@for (city of filteredCities; track city.code) {
						<mat-option [value]="city.code">{{ city.name }}</mat-option>
					}
				</mat-autocomplete>
				@if (shipperConsigneeForm.get('cityCode')?.hasError('required')) {
					<mat-error>{{ 'sli.mgmt.company.city.required' | translate }}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{ 'sli.mgmt.company.textualPostCode' | translate }}</mat-label>
				<input matInput formControlName="textualPostCode" />
			</mat-form-field>
		</div>

		<div class="row">
			<mat-form-field appearance="outline" class="col-6" floatLabel="always">
				<mat-label>{{ 'sli.mgmt.company.address' | translate }}</mat-label>
				<input matInput formControlName="locationName" required />
				@if (shipperConsigneeForm.get('locationName')?.hasError('required')) {
					<mat-error>{{ 'sli.mgmt.company.address.required' | translate }}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{ 'sli.mgmt.company.phoneNumber' | translate }}</mat-label>
				<input matInput formControlName="phoneNumber" required />
				@if (shipperConsigneeForm.get('phoneNumber')?.hasError('required')) {
					<mat-error>{{ 'sli.mgmt.company.phoneNumber.required' | translate }}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{ 'sli.mgmt.company.emailAddress' | translate }}</mat-label>
				<input matInput formControlName="emailAddress" />
				@if (shipperConsigneeForm.get('emailAddress')?.hasError('email')) {
					<mat-error>{{ 'sli.mgmt.company.pattern.email' | translate }}</mat-error>
				}
			</mat-form-field>
		</div>
	</form>
</div>
