<div class="orll-residents-detail">
	<form [formGroup]="form">
		<mat-accordion class="orll-residents-detail__panel">
			<mat-expansion-panel expanded="true">
				<mat-expansion-panel-header>
					<mat-panel-title>
						{{ 'system.server.matpanel.title.keycloak' | translate }}
					</mat-panel-title>
				</mat-expansion-panel-header>

				<div class="row">
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.neOneUrl' | translate }}</mat-label>
						<input matInput formControlName="neOneUrl" />
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.keycloakUrl' | translate }}</mat-label>
						<input matInput formControlName="keycloakUrl" />
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.grantType' | translate }}</mat-label>
						<input matInput formControlName="grantType" />
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.clientId' | translate }}</mat-label>
						<input matInput formControlName="clientId" />
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.clientSecret' | translate }}</mat-label>
						<input matInput formControlName="clientSecret" />
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.logisticsAgentUri' | translate }}</mat-label>
						<input matInput formControlName="logisticsAgentUri" />
					</mat-form-field>
				</div>
			</mat-expansion-panel>
		</mat-accordion>

		<mat-accordion class="orll-residents-detail__panel">
			<mat-expansion-panel expanded="true">
				<mat-expansion-panel-header>
					<mat-panel-title> {{ 'system.server.matpanel.title.server' | translate }} </mat-panel-title>
				</mat-expansion-panel-header>
				<div class="row">
					<mat-form-field appearance="outline" floatLabel="always" class="col-6">
						<mat-label>{{ 'system.server.matpanel.label.uri' | translate }}</mat-label>
						<input matInput formControlName="uri" />
					</mat-form-field>
					<div class="col-3">
						<button
							mat-raised-button
							color="primary"
							type="button"
							class="reverse-icon"
							[disabled]="!isFormValid"
							(click)="$event.stopPropagation(); getServerInfo()">
							<mat-icon>search</mat-icon>
							{{ 'system.server.button.retrieveServerInfo' | translate }}
						</button>
					</div>
				</div>

				<div class="row">
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.endpoint' | translate }}</mat-label>
						<input matInput formControlName="endpoint" />
					</mat-form-field>

					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.apiVersion' | translate }}</mat-label>
						<input matInput formControlName="apiVersion" />
					</mat-form-field>
				</div>

				<div class="row">
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.contentType' | translate }}</mat-label>

						<input matInput formControlName="contentType" />
					</mat-form-field>

					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.encoding' | translate }}</mat-label>
						<input matInput formControlName="encoding" />
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.language' | translate }}</mat-label>

						<input matInput formControlName="language" />
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="form-strech">
						<mat-label>{{ 'system.server.matpanel.label.ontology' | translate }}</mat-label>
						<input matInput formControlName="ontology" />
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.ontologyVersion' | translate }}</mat-label>
						<input matInput formControlName="ontologyVersion" />
					</mat-form-field>
				</div>
			</mat-expansion-panel>
		</mat-accordion>

		<mat-accordion class="orll-residents-detail__panel">
			<mat-expansion-panel expanded="true">
				<mat-expansion-panel-header>
					<mat-panel-title> {{ 'system.server.matpanel.title.organization' | translate }} </mat-panel-title>
				</mat-expansion-panel-header>
				@if (type === serverType.EXTERNAL) {
					<div class="row">
						<mat-form-field appearance="outline" floatLabel="always" class="col-6">
							<mat-label>{{ 'system.server.uri' | translate }}</mat-label>
							<input matInput formControlName="orgnizationUri" />
						</mat-form-field>
						<div class="col-3">
							<button
								mat-raised-button
								color="primary"
								type="button"
								class="reverse-icon"
								(click)="retrieveOrg()"
								[disabled]="!isFormValid">
								<mat-icon>search</mat-icon>
								{{ 'system.server.retrive.org.btn' | translate }}
							</button>
						</div>
					</div>
				}
				<div class="row">
					@if (type === serverType.RESIDENT) {
						<mat-form-field appearance="outline" floatLabel="always" class="col-2">
							<mat-label>{{ 'system.server.matpanel.label.id' | translate }}</mat-label>
							<input matInput formControlName="orgIdStr" />
						</mat-form-field>
					}
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.residentsType' | translate }}</mat-label>
						<mat-select formControlName="residentsType" (selectionChange)="residentsTypeValueChange($event)">
							@for (residentsType of filteredResidentsType; track residentsType.code) {
								<mat-option [value]="residentsType.code">{{ residentsType.name }}</mat-option>
							}
						</mat-select>
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.companyName' | translate }}</mat-label>
						<input matInput formControlName="companyName" />
					</mat-form-field>
					@if (isForwarder) {
						<mat-form-field appearance="outline" floatLabel="always" class="col-2">
							<mat-label>{{ 'system.server.matpanel.label.forwarderIATACode' | translate }}</mat-label>
							<input matInput formControlName="iataCargoAgentCode" />
						</mat-form-field>
					}
					@if (isCarrier) {
						<mat-form-field appearance="outline" floatLabel="always" class="col-2">
							<mat-label>{{ 'system.server.matpanel.label.airlineCode' | translate }}</mat-label>
							<input matInput formControlName="airlineCode" />
						</mat-form-field>
						<mat-form-field appearance="outline" floatLabel="always" class="col-2">
							<mat-label>{{ 'system.server.matpanel.label.airlinePrefix' | translate }}</mat-label>
							<input matInput formControlName="airlinePrefix" />
						</mat-form-field>
					}
				</div>

				<div class="row">
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.country' | translate }}</mat-label>

						<mat-select formControlName="country" (selectionChange)="countryValueChange($event)">
							@for (country of filteredCountries; track country.code) {
								<mat-option [value]="country.code">{{ country.name }}</mat-option>
							}
						</mat-select>
					</mat-form-field>

					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.province' | translate }}</mat-label>
						<mat-select formControlName="province" (selectionChange)="regionValueChange($event)">
							@for (province of filteredProvinces; track province.code) {
								<mat-option [value]="province.code">{{ province.name }}</mat-option>
							}
						</mat-select>
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.city' | translate }}</mat-label>
						<mat-select formControlName="city">
							@for (city of filteredCities; track city.code) {
								<mat-option [value]="city.code">{{ city.name }}</mat-option>
							}
						</mat-select>
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="form-strech">
						<mat-label>{{ 'system.server.matpanel.label.address' | translate }}</mat-label>
						<input matInput formControlName="address" />
					</mat-form-field>
					<mat-form-field appearance="outline" floatLabel="always" class="col-2">
						<mat-label>{{ 'system.server.matpanel.label.postCode' | translate }}</mat-label>
						<input matInput formControlName="postCode" />
					</mat-form-field>
				</div>
			</mat-expansion-panel>
		</mat-accordion>
	</form>
	<form [formGroup]="contactForm">
		<div formArrayName="contactList">
			@for (contactGroup of contactListArray.controls; track $index) {
				<div [formGroupName]="$index">
					<mat-accordion class="orll-residents-detail__panel">
						<mat-expansion-panel expanded="true">
							<mat-expansion-panel-header>
								<mat-panel-title> {{ 'system.server.matpanel.title.contact' | translate }} </mat-panel-title>
								<mat-panel-description class="d-flex justify-content-end">
									<button mat-icon-button color="primary" (click)="$event.stopPropagation(); deleteContact($index)">
										<mat-icon>delete</mat-icon>
									</button>
								</mat-panel-description>
							</mat-expansion-panel-header>
							@if (type === serverType.EXTERNAL) {
								<div class="row">
									<mat-form-field appearance="outline" floatLabel="always" class="col-6">
										<mat-label>{{ 'system.server.uri' | translate }}</mat-label>
										<input matInput formControlName="contactUri" />
									</mat-form-field>
									<div class="col-3">
										<button
											mat-raised-button
											color="primary"
											type="button"
											class="reverse-icon"
											[disabled]="!isFormValid"
											(click)="retrieveContact($index)">
											<mat-icon>
												<search></search>
											</mat-icon>
											{{ 'system.server.retrive.contact.btn' | translate }}
										</button>
									</div>
								</div>
							}
							<div class="row">
								<mat-form-field appearance="outline" floatLabel="always" class="col-2">
									<mat-label>{{ 'system.server.matpanel.label.contactRole' | translate }}</mat-label>

									<mat-select formControlName="contactRole">
										@for (role of contactRoles; track role.code) {
											<mat-option [value]="role.code">{{ role.name }}</mat-option>
										}
									</mat-select>
								</mat-form-field>

								<mat-form-field appearance="outline" floatLabel="always" class="col-2">
									<mat-label>{{ 'system.server.matpanel.label.jobTitle' | translate }}</mat-label>

									<input matInput formControlName="jobTitle" />
								</mat-form-field>

								<mat-form-field appearance="outline" floatLabel="always" class="col-2">
									<mat-label>{{ 'system.server.matpanel.label.contactName' | translate }}</mat-label>
									<input matInput formControlName="contactName" />
								</mat-form-field>
							</div>

							<div class="row">
								<mat-form-field appearance="outline" floatLabel="always" class="col-2">
									<mat-label>{{ 'system.server.matpanel.label.contactDetailType' | translate }}</mat-label>
									<mat-select formControlName="contactDetailType">
										@for (type of contactDetailTypes; track type?.code) {
											<mat-option [value]="type.code">{{ type.name }}</mat-option>
										}
									</mat-select>
								</mat-form-field>

								<mat-form-field appearance="outline" floatLabel="always" class="col-4">
									<mat-label>{{ 'system.server.matpanel.label.textualValue' | translate }}</mat-label>
									<input matInput formControlName="textualValue" />
								</mat-form-field>
							</div>

							<div class="row">
								<mat-form-field appearance="outline" floatLabel="always" class="col-2">
									<mat-label>{{ 'system.server.matpanel.label.contactDetailType' | translate }}</mat-label>
									<mat-select formControlName="contactDetailType1">
										@for (type of contactDetailTypes; track type?.code) {
											<mat-option [value]="type.code">{{ type.name }}</mat-option>
										}
									</mat-select>
								</mat-form-field>

								<mat-form-field appearance="outline" floatLabel="always" class="col-4">
									<mat-label>{{ 'system.server.matpanel.label.textualValue' | translate }}</mat-label>
									<input matInput formControlName="textualValue1" />
								</mat-form-field>
							</div>
						</mat-expansion-panel>
					</mat-accordion>
				</div>
			}
		</div>
		<button mat-stroked-button color="primary" class="reverse-icon" (click)="newContact()">
			{{ 'system.server.contact.new' | translate }}
			<mat-icon>add</mat-icon>
		</button>
	</form>
	<div class="d-flex">
		@if (type === serverType.EXTERNAL) {
			<button mat-raised-button color="warn" class="reverse-icon" (click)="deleteServer()">
				{{ 'system.server.button.deleteExternalServer' | translate }}
				<mat-icon>delete</mat-icon>
			</button>
		}
		<button mat-raised-button color="primary" class="reverse-icon delete-button" (click)="onSave()">
			<mat-icon>check</mat-icon>
			{{ 'system.server.button.save' | translate }}
		</button>
	</div>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
