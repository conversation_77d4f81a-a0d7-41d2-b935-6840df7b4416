<div class="orll-mawb-status iata-box">
	@if (showUpdate) {
		<div class="flex-fill d-flex justify-content-end">
			<button mat-stroked-button color="primary" (click)="showUpdatePage(false)" class="orll-mawb-status__update-log">
				{{ 'mawb.event.update.log.btn' | translate }}<mat-icon>update</mat-icon>
			</button>
			<button
				mat-flat-button
				color="primary"
				(click)="bulkUpdate()"
				[disabled]="updateBtnDisabled()"
				class="orll-mawb-status__bulk-update">
				{{ 'mawb.event.bulk.update.btn' | translate }}<mat-icon>upload</mat-icon>
			</button>
		</div>
	} @else {
		<button mat-stroked-button color="primary" (click)="showUpdatePage(true)" class="orll-mawb-status__back-to-update">
			{{ 'mawb.event.back.btn' | translate }}<mat-icon>arrow_back </mat-icon>
		</button>
	}

	@if (showUpdate) {
		<mat-divider class="orll-mawb-status__divider"></mat-divider>
		<div class="orll-mawb-status__mawb row">
			<div class="orll-mawb-status__checkbox_col col-4">
				<mat-checkbox
					[checked]="mawbStatus?.checked"
					(click)="$event.stopPropagation()"
					(keydown.enter)="$event.stopPropagation()"
					(change)="toggleMawb()"></mat-checkbox>
				{{ 'MAWB#' }}<a [href]="mawbStatus?.mawbId">{{ `${mawbStatus?.code ?? ''}` }}</a>
			</div>
			<div
				class="col-2"
				(click)="openHistory(mawbStatus?.mawbId, 'mawb')"
				(keydown.enter)="$event.stopPropagation()"
				class="orll-mawb-status__history">
				{{ 'partner.mgmt.latestStatus' | translate }}:

				<span class="orll-mawb-status__col_status">
					{{ mawbStatus?.latestStatus | valueOrNA }}
				</span>
			</div>
			<div class="col-2">{{ 'mawb.event.history.table.time' | translate }}:{{ mawbStatus?.eventDate | iataDateFormat }}</div>
			<div class="col-4 org-user">
				{{ 'mawb.event.history.table.user' | translate }}:{{ `${mawbStatus?.orgName ?? ''}:${mawbStatus?.userName ?? ''}` }}
			</div>
		</div>

		@for (hawb of hawbStatusList; track hawb.hawbId) {
			<div class="orll-mawb-status__hawb">
				<div class="orll-mawb-status__panel_header row">
					<div class="orll-mawb-status__checkbox_col col-4 ps-3">
						<mat-checkbox
							[checked]="hawb?.checked"
							(click)="$event.stopPropagation()"
							(keydown.enter)="$event.stopPropagation()"
							(change)="toggleHawb(hawb)"></mat-checkbox
						>{{ 'HAWB#' }}<a [href]="hawb?.hawbId">{{ hawb?.hawbNumber }}</a>
					</div>
					<div
						class="col-2"
						(click)="openHistory(hawb?.hawbId, 'hawb')"
						(keydown.enter)="$event.stopPropagation()"
						class="orll-mawb-status__history">
						{{ 'partner.mgmt.latestStatus' | translate }}:

						<span class="orll-mawb-status__col_status">
							{{ hawb?.latestStatus | valueOrNA }}
						</span>
					</div>
					<div class="col-2">{{ 'mawb.event.history.table.time' | translate }}:{{ hawb?.updateTime | iataDateFormat }}</div>
					<div class="col-2">
						{{ 'mawb.event.history.table.user' | translate }}:{{ `${`${hawb?.orgName ?? ''}:${hawb?.userName ?? ''}`}` }}
					</div>
					@if (hawb.opened) {
						<div
							class="col-2 flex-fill d-flex justify-content-end orll-mawb-status__btn"
							(click)="togglePanel(hawb)"
							(keydown)="togglePanel(hawb)">
							<mat-icon>keyboard_arrow_up</mat-icon>
						</div>
					} @else {
						<div
							class="col-2 flex-fill d-flex justify-content-end orll-mawb-status__btn"
							(click)="togglePanel(hawb)"
							(keydown)="togglePanel(hawb)">
							<mat-icon>keyboard_arrow_down</mat-icon>
						</div>
					}
				</div>
				@if (currentUserOrgType && !orgTypes.includes(currentUserOrgType) && hawb.opened) {
					<div class="orll-mawb-status__piece" (scroll)="onScroll($event, hawb)">
						@for (piece of hawb.pieceStatusList; track piece.pieceId) {
							<div class="orll-mawb-status__panel_header row">
								<div class="orll-mawb-status__checkbox_col col-4" checked="piece?.checked">
									<mat-checkbox
										[checked]="piece?.checked"
										(click)="$event.stopPropagation()"
										(keydown.enter)="$event.stopPropagation()"
										(change)="togglePiece(hawb, piece)"></mat-checkbox
									>{{ 'Piece#' }}<a [href]="piece?.pieceId">{{ piece?.description }}</a>
								</div>
								<div
									class="col-2"
									(click)="openHistory(piece?.pieceId, 'piece')"
									(keydown.enter)="$event.stopPropagation()"
									class="orll-mawb-status__history">
									{{ 'partner.mgmt.latestStatus' | translate }}:

									<span class="orll-mawb-status__col_status">
										{{ piece?.latestStatus | valueOrNA }}
									</span>
								</div>
								<div class="col-2">
									{{ 'mawb.event.history.table.time' | translate }}:{{ piece?.updateTime | iataDateFormat }}
								</div>
								<div class="col-2">
									{{
										'mawb.event.history.table.user' | translate
									}}:{{ `${piece?.orgName ?? ''}:${piece?.userName ?? ''}` }}
								</div>
								<div class="col-2 col-2 flex-fill d-flex justify-content-end"></div>
							</div>
						}
					</div>
				}
			</div>
		}
	} @else {
		<orll-table [columns]="updateLogColums" [param]="logParam" [service]="statusService"></orll-table>
	}
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
