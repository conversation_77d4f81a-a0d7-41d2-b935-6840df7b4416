{"name": "iata-orll-fe", "version": "1.0.185", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "build-test": "ng build --configuration test", "build-staging": "ng build --configuration staging", "build-prod": "ng build --configuration production", "test": "ng test", "test:ci": "ng test --watch=false --progress=false --browsers=ChromeHeadlessCI --code-coverage --source-map=false", "lint": "tsc --noEmit && eslint . --ext ts --quiet --fix"}, "private": true, "dependencies": {"@angular/animations": "^19.2.4", "@angular/cdk": "^19.2.7", "@angular/common": "^19.2.4", "@angular/compiler": "^19.2.4", "@angular/core": "^19.2.4", "@angular/forms": "^19.2.4", "@angular/material": "^19.2.7", "@angular/material-luxon-adapter": "^19.2.7", "@angular/platform-browser": "^19.2.4", "@angular/platform-browser-dynamic": "^19.2.4", "@angular/router": "^19.2.4", "@azure/msal-angular": "^4.0.8", "@azure/msal-browser": "^4.9.1", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "chart.js": "^4.5.0", "js-base64": "^3.7.7", "ng2-charts": "^8.0.0", "rxjs": "^7.8.2", "tslib": "^2.8.1", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.5", "@angular-eslint/builder": "^19.3.0", "@angular-eslint/eslint-plugin": "^19.3.0", "@angular-eslint/eslint-plugin-template": "^19.3.0", "@angular-eslint/schematics": "^19.3.0", "@angular-eslint/template-parser": "^19.3.0", "@angular/cli": "^19.2.5", "@angular/compiler-cli": "^19.2.4", "@stylistic/eslint-plugin": "^2.13.0", "@types/jasmine": "^5.1.7", "@types/luxon": "^3.4.2", "angular-eslint": "^19.3.0", "eslint": "^9.7.0", "eslint-config-prettier": "^10.1.1", "jasmine-core": "^5.6.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "prettier": "^3.5.3", "typescript": "^5.8.2", "typescript-eslint": "^8.28.0"}, "overrides": {"eslint": "^9.7.0"}}