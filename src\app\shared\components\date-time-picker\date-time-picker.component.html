<mat-form-field appearance="outline" class="width-100">
	<mat-label>{{ label | translate }}</mat-label>
	<input
		matInput
		[matDatepicker]="picker"
		[formControl]="selection"
		#pickerInput
		readonly
		(dateChange)="onDateChange($event.value)"
		(blur)="onBlur()" />
	<button
		matSuffix
		mat-icon-button
		color="primary"
		type="button"
		matTooltip="Clear value"
		matTooltipPosition="above"
		(click)="eraseValue($event)">
		<mat-icon class="material-symbols-outlined">close</mat-icon>
	</button>
	<mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
	<mat-datepicker #picker>
		<mat-datepicker-actions>
			<div class="time-picker">
				<input #inlineTime type="time" [value]="selectedTime" (change)="onTimeChange($event)" class="time-picker-input" />
			</div>
			<button mat-button matDatepickerApply>
				{{ 'common.dialog.ok' | translate }}
			</button>
		</mat-datepicker-actions>
	</mat-datepicker>
	@if (selection.hasError('required') && selection.touched) {
		<mat-error>{{ 'validators.required' | translate: { field: label | translate } }}</mat-error>
	}
</mat-form-field>
