<div class="booking-option-create">
	<div class="booking-option-create__detail iata-box">
		<orll-booking-option-request-detail [detailInfo]="detailInfo" [quote]="true"></orll-booking-option-request-detail>
	</div>
	<div class="booking-option-create__option">
		<form [formGroup]="optionForm">
			<div formArrayName="options">
				@for (bookingOption of optionForm.controls.options.controls; track $index) {
					<div class="iata-box">
						<div class="row sub-title">
							<div class="col-1">{{ 'booking.dialog.title.option' | translate }}</div>
							@if (!bookingOption.controls.showOption.value) {
								<div class="col-8">
									{{ 'booking.option.price.title' | translate }}
								</div>
								<div class="col-2 sub-total">
									{{ 'booking.option.grand.total' | translate }}{{ bookingOption.controls.grandTotal.value ?? '' }}
								</div>
							}
							<mat-icon
								(click)="bookingOption.controls.showOption.value = !bookingOption.controls.showOption.value"
								(keydown.enter)="$event.stopPropagation()"
								color="primary"
								class="option-toggle-btn">
								@if (bookingOption.controls.showOption.value) {
									expand_less
								} @else {
									expand_more
								}
							</mat-icon>
						</div>
						@if (bookingOption.controls.showOption.value) {
							<div [formGroupName]="$index" class="booking-option-create__option-item">
								<div class="row price-summary">
									<div class="col-10">
										{{ 'booking.option.price.title' | translate }}
									</div>
									<div class="col-2">
										{{ 'booking.option.grand.total' | translate }}{{ bookingOption.controls.grandTotal.value ?? '' }}
									</div>
								</div>
								<div formArrayName="priceList">
									@for (price of getPriceList($index).controls; track $index) {
										<div [formGroupName]="$index">
											<div class="booking-option-create__option-price">
												<div class="row">
													<mat-form-field class="col-2">
														<mat-label>{{ 'booking.option.form.charge.type' | translate }}</mat-label>
														<mat-select formControlName="chargeType">
															@for (code of chargeTypes; track code.code) {
																<mat-option [value]="code.code">{{ code.name }}</mat-option>
															}
														</mat-select>
														@if (price.get('chargeType').hasError('required')) {
															<mat-error>
																{{
																	'validators.required'
																		| translate
																			: { field: 'booking.option.form.charge.type' | translate }
																}}
															</mat-error>
														}
													</mat-form-field>
													<mat-form-field class="col-2">
														<mat-label>{{ 'booking.option.form.charge.rate.class' | translate }}</mat-label>
														<mat-select formControlName="rateClassCode">
															@for (code of rateClassCodes; track code.code) {
																<mat-option [value]="code.code">{{ code.name }}</mat-option>
															}
														</mat-select>
														@if (price.get('rateClassCode').hasError('required')) {
															<mat-error>
																{{
																	'validators.required'
																		| translate
																			: { field: 'booking.option.form.charge.rate.class' | translate }
																}}
															</mat-error>
														}
													</mat-form-field>
													<mat-form-field class="col-2">
														<mat-label>{{ 'booking.option.form.rate' | translate }}</mat-label>
														<input
															matInput
															formControlName="subTotal"
															(change)="updateGrandTotal(bookingOption)" />
														@if (price.get('subTotal').hasError('required')) {
															<mat-error>
																{{
																	'validators.required'
																		| translate: { field: 'booking.option.form.rate' | translate }
																}}
															</mat-error>
														}
														@if (price.get('subTotal').hasError('pattern')) {
															<mat-error>
																{{ 'validators.maxDecimal2' | translate }}
															</mat-error>
														}
													</mat-form-field>

													<mat-form-field class="col-2">
														<mat-label>{{ 'booking.option.form.payment.type' | translate }}</mat-label>
														<mat-select formControlName="chargePaymentType">
															@for (code of chargePaymentTypes; track code.code) {
																<mat-option [value]="code.code">{{ code.name }}</mat-option>
															}
														</mat-select>
													</mat-form-field>
													<div class="col-4">
														@if (!bookingOption.controls.disableOption.value) {
															@if ($index === 0) {
																<button
																	mat-stroked-button
																	color="primary"
																	(click)="addPrice(bookingOption)"
																	class="btn-pointer">
																	<mat-icon>add</mat-icon>
																	{{ 'booking.option.add.charge.btn' | translate }}
																</button>
															} @else {
																<mat-icon
																	color="primary"
																	class="btn-pointer"
																	(click)="delPrice(bookingOption, $index)"
																	(keydown.enter)="delPrice(bookingOption, $index)"
																	>delete</mat-icon
																>
															}
														}
													</div>
												</div>
											</div>
										</div>
									}
								</div>
								<mat-divider></mat-divider>
								<div formArrayName="transportLegsList" class="booking-option-create__option-transport">
									<div class="row sub-title">
										{{ 'booking.option.subTitle.itinerary' | translate }}
									</div>
									@for (tran of getTransportLegsList($index).controls; track $index) {
										@if ($index > 0) {
											<div class="row flight-section">
												<svg
													width="10"
													height="36"
													viewBox="0 0 10 36"
													fill="none"
													xmlns="http://www.w3.org/2000/svg">
													<path
														d="M0 36C1.1333e-07 33.4073 1.97333 31.2753 4.5 31.0244L4.5 4.97559C1.97333 4.72474 1.46028e-06 2.59268 1.57361e-06 0L10 4.37114e-07C10 2.59268 8.02668 4.72474 5.5 4.97559L5.5 31.0244C8.02667 31.2753 10 33.4073 10 36H0Z"
														fill="#7985FF" />
												</svg>
											</div>
										}
										<div [formGroupName]="$index">
											<div class="row">
												<mat-form-field appearance="outline" class="col-2" floatLabel="always">
													<mat-label>{{ 'booking.option.form.departure' | translate }}</mat-label>
													<mat-select formControlName="departureLocation">
														@for (code of locationList; track code.code) {
															<mat-option [value]="code.code">{{ code.name }}</mat-option>
														}
													</mat-select>
													@if (tran.get('departureLocation').hasError('required')) {
														<mat-error>
															{{
																'validators.required'
																	| translate: { field: 'booking.option.form.departure' | translate }
															}}
														</mat-error>
													}
												</mat-form-field>
												<mat-form-field appearance="outline" class="col-2" floatLabel="always">
													<mat-label>{{ 'booking.option.form.arrival' | translate }}</mat-label>
													<mat-select formControlName="arrivalLocation">
														@for (code of locationList; track code.code) {
															<mat-option [value]="code.code">{{ code.name }}</mat-option>
														}
													</mat-select>
													@if (tran.get('arrivalLocation').hasError('required')) {
														<mat-error>
															{{
																'validators.required'
																	| translate: { field: 'booking.option.form.arrival' | translate }
															}}
														</mat-error>
													}
												</mat-form-field>
												<mat-form-field class="col-2">
													<mat-label>{{ 'booking.option.form.airline.code' | translate }}</mat-label>
													<input matInput formControlName="airlineCode" />
												</mat-form-field>
												<mat-form-field class="col-2">
													<mat-label>{{ 'booking.option.form.flight.number' | translate }}</mat-label>
													<input matInput formControlName="transportIdentifier" />
													@if (tran.get('transportIdentifier').hasError('required')) {
														<mat-error>
															{{
																'validators.required'
																	| translate: { field: 'booking.option.form.flight.number' | translate }
															}}
														</mat-error>
													}
												</mat-form-field>
											</div>
											<div class="row">
												<div class="col-2">
													<orll-date-time-picker
														[label]="'booking.option.form.departure.time'"
														formControlName="departureDate"
														[validatorFn]="validators.required"></orll-date-time-picker>
												</div>
												<div class="col-2">
													<orll-date-time-picker
														[label]="'booking.option.form.arrival.time'"
														formControlName="arrivalDate"></orll-date-time-picker>
												</div>
											</div>
										</div>
									}
									<mat-divider></mat-divider>
									<div class="row itinerary-btn-panel">
										@if (!bookingOption.controls.disableOption.value) {
											@if (getTransportLegsList($index).controls.length < 4) {
												<button
													mat-stroked-button
													color="primary"
													(click)="addTrans(bookingOption)"
													class="add-btn">
													<mat-icon>add</mat-icon>
													{{ 'booking.option.add.itinerary' | translate }}
												</button>
											}
											@if (getTransportLegsList($index).controls.length > 1) {
												<button
													mat-stroked-button
													color="primary"
													(click)="delTrans(bookingOption)"
													class="remove-btn">
													<mat-icon>remove</mat-icon>
													{{ 'booking.option.del.itinerary' | translate }}
												</button>
											}
										}
									</div>
								</div>
								<mat-divider></mat-divider>
								<div class="booking-option-create__option-other row">
									<mat-form-field class="col-2">
										<mat-label>{{ 'booking.option.form.product.code' | translate }}</mat-label>
										<input matInput formControlName="productDescription"
									/></mat-form-field>
									<div class="col-2">
										<orll-date-time-picker
											[label]="'booking.option.form.offer.valid.from'"
											formControlName="offerValidFrom"></orll-date-time-picker>
									</div>
									<div class="col-2">
										<orll-date-time-picker
											[label]="'booking.option.form.offer.valid.to'"
											formControlName="offerValidTo" />
									</div>
								</div>
							</div>
						}
					</div>
				}
			</div>
		</form>
	</div>
	<div class="row booking-option-create__btn-panel">
		<button mat-stroked-button color="primary" (click)="addOptionGroup()">
			<mat-icon>add</mat-icon>
			{{ 'booking.option.add.booking.option.btn' | translate }}
		</button>
		<button mat-flat-button color="primary" (click)="sendOpitonRequestToForwarder()" class="to-forwarder-btn">
			<mat-icon>send</mat-icon>
			{{ 'booking.option.to.forwarder' | translate }}
		</button>
	</div>
</div>

@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
