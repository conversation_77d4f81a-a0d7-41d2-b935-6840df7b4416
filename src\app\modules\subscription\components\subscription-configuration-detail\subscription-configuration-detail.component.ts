import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { SubscriptionConfigurationService } from '../../services/subscription-configuration.service';
import { DictObj, SubscriptionConfigDetailObj, SubscriptionConfigurationListObj } from '../../models/subscription.model';
import { MatButtonModule } from '@angular/material/button';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DatePipe } from '@angular/common';
import { MatDividerModule } from '@angular/material/divider';
import { provideLuxonDateAdapter } from '@angular/material-luxon-adapter';
import { DateTime } from 'luxon';
import { DATE_TIME_FORMAT } from '@shared/models/constant';
import { formatDateTime } from '@shared/utils/common.utils';

@Component({
	selector: 'orll-subscription-configuration-detail',
	imports: [
		MatDialogModule,
		MatButtonModule,
		ReactiveFormsModule,
		SpinnerComponent,
		TranslateModule,
		MatFormFieldModule,
		MatIconModule,
		MatSelectModule,
		MatInputModule,
		MatCheckboxModule,
		MatDatepickerModule,
		MatDividerModule,
	],
	providers: [
		provideLuxonDateAdapter({
			parse: {
				dateInput: 'MM/dd/yyyy',
			},
			display: {
				dateInput: 'MM/dd/yyyy',
				monthYearLabel: 'yyyy',
				dateA11yLabel: 'LL',
				monthYearA11yLabel: 'MM yyyy',
			},
		}),
		DatePipe,
	],
	templateUrl: './subscription-configuration-detail.component.html',
	styleUrl: './subscription-configuration-detail.component.scss',
})
export class SubscriptionConfigurationDetailComponent implements OnInit {
	configurationForm: FormGroup = new FormGroup({
		topicType: new FormControl<string>('', [Validators.required]),
		topic: new FormControl<string>('', [Validators.required]),
		subscriptionEventType: new FormGroup({}),
		description: new FormControl<string>('', [Validators.required]),
		expiresAt: new FormControl<string>('', [Validators.required]),
	});
	isEdit = false;
	dataLoading = false;

	topicTypeOptions: DictObj[] = [];

	topicOptions: string[] = [];

	eventTypeOptions: DictObj[] = [];

	constructor(
		private readonly subscriptionService: SubscriptionConfigurationService,
		@Inject(MAT_DIALOG_DATA) public data: SubscriptionConfigurationListObj,
		private readonly dialogRef: MatDialogRef<SubscriptionConfigurationDetailComponent>
	) {}

	ngOnInit(): void {
		//get dict list
		this.subscriptionService.getTopicOptions().subscribe((res) => {
			this.topicOptions = res;
		});
		this.subscriptionService.getTopicTypeOptions().subscribe((res) => {
			this.topicTypeOptions = res;
		});

		this.subscriptionService.getEventTypeOptions().subscribe((res) => {
			this.eventTypeOptions = res;
			if (res) {
				this.buildEventTypeFormGroup(res);
				if (this.data) {
					this.isEdit = true;
					this.getConfiguration(this.data);
				}
			}
		});
	}

	buildEventTypeFormGroup(options: DictObj[]) {
		const eventsGroup = this.configurationForm.get('subscriptionEventType') as FormGroup;
		options.forEach((opt) => {
			eventsGroup.addControl(opt.code, new FormControl(false));
		});
		eventsGroup.setValidators([this.atLeastOneChecked().bind(this)]);
	}

	atLeastOneChecked() {
		return (group: any) => {
			const checked = Object.values(group.value).some((v: any) => v === true);
			return checked ? null : { atLeastOneRequired: true };
		};
	}

	getConfiguration(detail: SubscriptionConfigurationListObj) {
		if (!detail?.id) {
			return;
		}
		this.dataLoading = true;
		this.subscriptionService.getConfiguration(detail.id).subscribe({
			next: (res) => {
				this.dataLoading = false;
				this.configurationForm.patchValue(res);
				this.configurationForm.patchValue({ expiresAt: DateTime.fromFormat(res.expiresAt, DATE_TIME_FORMAT) });
				if (res.subscriptionEventTypeList) {
					const formGroup = this.configurationForm.get('subscriptionEventType') as FormGroup;
					res.subscriptionEventTypeList.forEach((item) => {
						const eventKey = item.eventName.toLocaleUpperCase();
						formGroup?.setValue({ ...formGroup.value, [eventKey]: item.checked === 'true' });
					});
				}
			},
			error: () => (this.dataLoading = false),
		});
	}

	saveConfiguration() {
		if (this.configurationForm.invalid) {
			this.configurationForm.markAllAsTouched();
			return;
		}

		this.dataLoading = true;
		this.subscriptionService.saveConfiguration(this.getFormData()).subscribe({
			next: () => {
				this.dataLoading = false;
				this.dialogRef.close(true);
			},
			error: () => {
				this.dataLoading = false;
			},
		});
	}

	getFormData(): SubscriptionConfigDetailObj {
		const events = this.configurationForm.value.subscriptionEventType
			? Object.keys(this.configurationForm.value.subscriptionEventType).map((key) => ({
					eventName: key,
					checked: (!!this.configurationForm.value.subscriptionEventType[key]).toString(),
				}))
			: [];
		const param: SubscriptionConfigDetailObj = {
			topic: this.configurationForm.value.topic,
			topicType: this.topicTypeOptions.find((item) => item.code === this.configurationForm.value.topicType)?.name ?? '',
			subscriptionEventType: events,
			description: this.configurationForm.value.description,
			subscriptionType: '',
			expiresAt: formatDateTime(this.configurationForm.value.expiresAt.toString()),
			userId: '',
			subscriberId: '',
			createAt: '',
		};
		if (this.data) {
			param.id = this.data.id;
		}
		return param;
	}
}
